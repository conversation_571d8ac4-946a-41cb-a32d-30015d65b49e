<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tw.org.must.must.mapper.dist.DistAutopayNetPayMemberMapper">
    <resultMap id="BaseResultMap" type="tw.org.must.must.model.dist.DistAutopayNetPayMember">

    </resultMap>
    <select id="groupDistAutopayNetPayMember" resultType="java.util.Map">
        SELECT dist_no as distNo,tax_year as taxYear,pay_date as payDate,SUM(local_taxable_income) as
        sumOfLocalTaxableIncome,
        SUM(overseas_taxable_income) as sumOfOverseasTaxableIncome,SUM(pay_amount) as
        sumOfPayAmount,SUM(sales_tax_amount) as sumOfSalesTaxAmount,
        SUM(withheld_tax) as sumOfWithheldTax,SUM(commission_amount) as sumOfCommissionAmount,SUM(non_taxable_income) as
        sumOfNonTaxableIncome
        FROM dist_autopay_net_pay_member
        WHERE is_pay = 'Y' AND ip_base_no = #{ipBaseNo} AND autopay_no = #{autopayNo}
        <if test="taxStart != null">
            and tax_year <![CDATA[ >= ]]> #{taxStart}
        </if>
        <if test="taxEnd != null">
            and tax_year <![CDATA[ <= ]]> #{taxEnd}
        </if>
        GROUP BY dist_no,pay_date,tax_year
        ORDER BY tax_year asc

    </select>

    <select id="getDistAutoPay750List" resultType="tw.org.must.must.model.dist.vo.v750.DistAutoPay750Vo">
        SELECT distinct danpm.autopay_no                                                      AS autopayNo,
                        danpm.pa_name_no                                                      AS paNameNo,
                        pa_name                                                               AS paName,
                        IFNULL(danpm.royalty_amount, 0) + IFNULL(danpm.adj_royalty_amount, 0) AS balance,
                        danpm.bank_account_name                                               AS bankAccountName,
                        danpm.bank_no                                                         AS bankCode,
                        danpm.bank_account_no                                                 AS accountNumber,
                        danpm.dist_no                                                         AS distNo,
                        rb.bank_name                                                          AS bankName,
                        rbb.branch_no                                                         AS branchNo,
                        danpm.pay_amount                                                      AS payAmount
        FROM dist_autopay_net_pay_member AS danpm
                 LEFT JOIN ref_bank rb ON rb.bank_no = danpm.bank_no
                 LEFT JOIN mbr_member_bank_acc mmbc ON mmbc.ip_base_no = danpm.ip_base_no
                 LEFT JOIN ref_bank_branch rbb ON rbb.bank_no = rb.bank_no AND rbb.branch_no = mmbc.bank_branch_no
        where danpm.autopay_no = #{autopayNo}
        AND mmbc.status = 1
        <if test="paNameNoList != null and paNameNoList.size() > 0">
            AND danpm.pa_name_no IN
            <foreach collection="paNameNoList" item="paNameNo" open="(" separator="," close=")">
                #{paNameNo}
            </foreach>
        </if>
        <if test="orderWay != null and orderWay != ''">
            <choose>
                <when test="orderWay == 'paNameNo'">
                    ORDER BY danpm.pa_name_no
                </when>
                <when test="orderWay == 'paName'">
                    ORDER BY danpm.pa_name
                </when>
                <when test="orderWay == 'balance'">
                    ORDER BY (IFNULL(danpm.royalty_amount, 0) + IFNULL(danpm.adj_royalty_amount, 0)) DESC
                </when>
                <when test="orderWay == 'bankName'">
                    ORDER BY rb.bank_name
                </when>
                <when test="orderWay == 'bankCode'">
                    ORDER BY danpm.bank_no
                </when>
                <when test="orderWay == 'accountNumber'">
                    ORDER BY danpm.bank_account_no
                </when>
                <when test="orderWay == 'distNo'">
                    ORDER BY danpm.dist_no
                </when>
                <when test="orderWay == 'payAmount'">
                    ORDER BY danpm.pay_amount DESC
                </when>
                <otherwise>
                    ORDER BY danpm.pa_name_no
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="getDistAutoPay880List" resultType="tw.org.must.must.model.dist.vo.v750.DistAutoPay750Vo">
        SELECT distinct danpm.autopay_no AS autopayNo,
        danpm.pa_name_no AS paNameNo,
        pa_name AS paName,
        IFNULL(danpm.royalty_amount, 0) + IFNULL(danpm.adj_royalty_amount, 0) AS balance,
        danpm.bank_account_name AS bankAccountName,
        danpm.bank_no AS bankCode,
        danpm.bank_account_no AS accountNumber,
        danpm.dist_no AS distNo,
        rb.bank_name AS bankName,
        rbb.branch_no AS branchNo,
        danpm.pay_amount AS payAmount,
        danpm.payment_method AS payMethod,
        mmba.payment_desc AS payDescription
        FROM dist_autopay_net_pay_member AS danpm
        LEFT JOIN ref_bank rb ON rb.bank_no = danpm.bank_no
        LEFT JOIN mbr_member_bank_acc mmbc ON mmbc.ip_base_no = danpm.ip_base_no
        LEFT JOIN ref_bank_branch rbb ON rbb.bank_no = rb.bank_no AND rbb.branch_no = mmbc.bank_branch_no
        LEFT JOIN mbr_member_bank_acc mmba ON mmba.ip_base_no = danpm.ip_base_no
        where danpm.autopay_no = #{autopayNo}
        <if test="distNo !=null and distNo !=''">
            and danpm.dist_no = #{distNo}
        </if>
        <if test="ipBaseNo != null and ipBaseNo !=''">
            and danpm.ip_base_no = #{ipBaseNo}
        </if>
        <if test="paNameNo !=null and paNameNo !=''">
            and danpm.pa_name_no = #{paNameNo}
        </if>
    </select>

    <select id="getDetailMem800" resultType="tw.org.must.must.model.dist.vo.v800.mem.DetailMem800">
        SELECT d.payment_method AS payMethod,
        d.pa_name AS paName,
        d.pa_name_no AS paNameNo,
        mmbc.account_name AS payee,
        mmbc.payment_desc AS paymentDesc
        FROM dist_autopay_net_pay_member d
        LEFT JOIN ref_bank rb ON rb.bank_no = d.bank_no
        LEFT JOIN mbr_member_bank_acc mmbc ON mmbc.ip_base_no = d.ip_base_no
        where d.autopay_no = #{autopayNo}
        <if test="distNo !=null and distNo !=''">
            and d.dist_no = #{distNo}
        </if>
        <if test="paNameNo != null and paNameNo !=''">
            and d.pa_name_no = #{paNameNo}
        </if>
        GROUP BY paNameNo
    </select>
    <select id="getDistNoDetailMem800" resultType="tw.org.must.must.model.dist.vo.v800.mem.DistNoDetailMem800">
        SELECT d.dist_no AS distNo,
        d.royalty_amount AS royalties,
        d.pay_amount AS payAbleAmount,
        d.commission_rate AS admissionRate,
        ABS(d.commission_amount) AS admissionAmount
        FROM dist_autopay_net_pay_member d
        where d.autopay_no = #{autopayNo}
        <if test="distNo !=null and distNo !=''">
            and d.dist_no = #{distNo}
        </if>
        <if test="paNameNo != null and paNameNo !=''">
            and d.pa_name_no = #{paNameNo}
        </if>
    </select>
</mapper>