package tw.org.must.must.web.controller.dist;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.JRException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tw.org.must.must.common.util.result.SuccessResult;
import tw.org.must.must.core.service.dist.DistAutopayBaseService;
import tw.org.must.must.core.service.dist.DistAutopayNetPayMemberService;
import tw.org.must.must.core.service.dist.DistAutopayNetPaySocietyService;
import tw.org.must.must.model.dist.DistAutopayNetPayMember;
import tw.org.must.must.model.dist.vo.DistAutoPay750Request;
import tw.org.must.must.model.dist.vo.Fsris100Request;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Date;
import java.util.List;

/**
 * @Author: hanDa
 * @Date: 2021/5/19 14:50
 * @Version:1.0
 * @Description:
 */
@RestController
@Api(tags = "支付报表")
@RequestMapping("/dist/autoPay/Report")
public class DistAutoPayReportController {
    @Autowired
    private DistAutopayBaseService distAutopayBaseService;
    @Autowired
    private DistAutopayNetPayMemberService distAutopayNetPayMemberService;
    @Autowired
    private DistAutopayNetPaySocietyService distAutopayNetPaySocietyService;

    @ApiOperation(value = "autopay no 列表")
    @GetMapping("/autoPayNos")
    public ResponseEntity getAutoPayNoList(@RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                           @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
                                           @RequestParam(value = "autopayNo", required = false) String autopayNo) {
        PageHelper.startPage(pageNum,pageSize);
        return ResponseEntity.ok(new SuccessResult<>(new PageInfo(distAutopayBaseService.getAutoPayNoList(autopayNo))));
    }


    @ApiOperation(value = "匯款(DIVA750) pdf导出")
    @PostMapping("/reportDistAutoPay750")
    public void reportDistAutoPay750(@RequestBody DistAutoPay750Request request, HttpServletResponse httpServletResponse
    ) throws FileNotFoundException, JRException {
        distAutopayNetPayMemberService.reportDistAutoPay750(request, httpServletResponse);
    }

    @ApiOperation(value = "支票報表(DIVA880 總表) pdf导出")
    @GetMapping("/reportDistAutoPay880")
    public void reportDistAutoPay880(@RequestParam("autopayNo") String autopayNo,
                                     HttpServletResponse httpServletResponse,
                                     @RequestParam(value = "autopayDescription", required = false) String autopayDescription,
                                     @RequestParam(value = "ipBaseNo", required = false) String ipBaseNo,
                                     @RequestParam(value = "paNameNo", required = false) String paNameNo,
                                     @RequestParam(value = "distNo", required = false) String distNo
    ) throws FileNotFoundException, JRException {
        distAutopayNetPayMemberService.reportDistAutoPay880(autopayNo, autopayDescription, httpServletResponse, ipBaseNo, paNameNo, distNo);
    }

    @ApiOperation(value = "支票報表(DIVA800明細)-协会 pdf导出")
    @GetMapping("/reportDistAutoPaySoc800")
    public void reportDistAutoPaySoc800(HttpServletResponse httpServletResponse,
                                        @RequestParam("autopayNo") String autopayNo,
                                        @RequestParam(value = "autopayDescription", required = false) String autopayDescription,
                                        @RequestParam(value = "societyCode", required = false) String societyCode,
                                        @RequestParam(value = "distNo", required = false) String distNo) throws FileNotFoundException, JRException {
        distAutopayNetPaySocietyService.reportDistAutoPaySoc800(httpServletResponse, autopayNo, autopayDescription, societyCode, distNo);
    }

    @ApiOperation(value = "支票報表(DIVA800明細)-会员 pdf导出")
    @GetMapping("/reportDistAutoPayMem800")
    public void reportDistAutoPayMem800(HttpServletResponse httpServletResponse,
                                        @RequestParam("autopayNo") String autopayNo,
                                        @RequestParam(value = "autopayDescription", required = false) String autopayDescription,
                                        @RequestParam(value = "paNameNo", required = false) String paNameNo,
                                        @RequestParam(value = "distNo", required = false) String distNo) throws Exception {
        distAutopayNetPayMemberService.reportDistAutoPayMem800(httpServletResponse, autopayNo, autopayDescription, paNameNo, distNo);
    }

    @ApiOperation(value = "【支付】海外收據(DIVA780，PRS有貸款協會項目) pdf导出")
    @GetMapping("/reportDistAutoPaySoc780")
    public void reportDistAutoPaySoc780(HttpServletResponse httpServletResponse,
                                        @RequestParam("autopayNo") String autopayNo,
                                        @RequestParam(value = "autopayDescription", required = false) String autopayDescription,
                                        @RequestParam(value = "societyCode", required = false) Integer societyCode,
                                        @RequestParam(value = "distNo", required = false) String distNo,
                                        @RequestParam(value = "bankInfo", required = false, defaultValue = "true") Boolean bankInfo,
                                        @RequestParam(value = "autopayDate", required = false) String autopayDate,
                                        @RequestParam(value = "groupBySociety", required = false, defaultValue = "false") Boolean groupBySociety) throws Exception {
        distAutopayNetPaySocietyService.reportDistAutoPaySoc780(httpServletResponse, autopayNo, autopayDescription, societyCode, distNo, bankInfo, autopayDate, groupBySociety);
    }

    @ApiOperation(value = "【支付】海外收據(DIVA780會員版，帶貨幣格式化) pdf导出")
    @GetMapping("/reportDistAutoPay780")
    public void reportDistAutoPay780(HttpServletResponse httpServletResponse,
                                     @RequestParam("autopayNo") String autopayNo,
                                     @RequestParam(value = "autopayDescription", required = false) String autopayDescription,
                                     @RequestParam(value = "societyCode", required = false) Integer societyCode,
                                     @RequestParam(value = "distNo", required = false) String distNo,
                                     @RequestParam(value = "bankInfo", required = false, defaultValue = "true") Boolean bankInfo,
                                     @RequestParam(value = "autopayDate", required = false) String autopayDate,
                                     @RequestParam(value = "groupBySociety", required = false, defaultValue = "false") Boolean groupBySociety) throws FileNotFoundException, JRException {
        distAutopayNetPayMemberService.reportDistAutoPay780(httpServletResponse, autopayNo, autopayDescription, societyCode, distNo, bankInfo, autopayDate, groupBySociety);
    }

    @ApiOperation(value = "【支付】收據(DIVA730年度使用報酬分配明細表)-支票 pdf导出")
    @GetMapping("/reportDistAutoPayMemCheck730")
    public void reportDistAutoPayMemCheck780(HttpServletResponse httpServletResponse,
                                             @RequestParam("autopayNo") String autopayNo,
                                             @RequestParam(value = "payDate", required = false) Date payDate,
                                             @RequestParam(value = "paNameNoList", required = false) List<String> paNameNoList,
                                             @RequestParam(value = "distNo", required = false) String distNo) throws Exception {
        distAutopayNetPayMemberService.reportDistAutoPayMemCheck730(httpServletResponse, autopayNo, paNameNoList, distNo, payDate);
    }


    @ApiOperation(value = "【支付】收據(DIVA730年度使用報酬分配明細表)-支票 pdf导出(根据paNameNo分组)")
    @PostMapping("/reportDistAutoPayMemCheck730Group")
    public ResponseEntity reportDistAutoPayMemCheck730Group( @RequestBody DistAutopayNetPayMember distAutopayNetPayMember)  {
        distAutopayNetPayMemberService.reportDistAutoPayMemCheck730( distAutopayNetPayMember);
        return ResponseEntity.ok(new SuccessResult<>("====="));
    }

    @ApiOperation(value = "【支付】收據(DIVA730年度使用報酬分配明細表)-汇款 pdf导出")
    @GetMapping("/reportDistAutoPayMem730")
    public void reportDistAutoPayMem780(HttpServletResponse httpServletResponse,
                                        @RequestParam("autopayNo") String autopayNo,
                                        @RequestParam(value = "autopayDescription", required = false) String autopayDescription,
                                        @RequestParam(value = "paNameNo", required = false) String paNameNo,
                                        @RequestParam(value = "distNo", required = false) String distNo) throws Exception {
        distAutopayNetPayMemberService.reportDistAutoPayMem730(httpServletResponse, autopayNo, autopayDescription, paNameNo, distNo);
    }


    @ApiOperation(value = "【支付】sales tax report（DIVA700）团体会员请款单 pdf导出")
    @PostMapping("/reportDistAutoPayMem700")
    public void reportDistAutoPayMem700(HttpServletResponse httpServletResponse,
                                        @RequestBody List<String> paNameNoList) throws Exception {
        distAutopayNetPayMemberService.exportSaleTaxReport(httpServletResponse, paNameNoList);
    }

    @ApiOperation(value = "【支付】银行txt导出")
    @GetMapping("/reportTxtReport")
    public void reportTxtReport(HttpServletResponse httpServletResponse,
                                        @RequestParam(value = "autopayNo") String autopayNo) {
        distAutopayNetPayMemberService.exportTxtReport(httpServletResponse, autopayNo);
    }

    @ApiOperation(value = "【支付】银行txt导出")
    @PostMapping("/exportFSRIS100MemberReport")
    public void exportFSRIS100MemberReport(HttpServletResponse httpServletResponse,
                                @RequestBody Fsris100Request fsris100Request) {
        distAutopayNetPayMemberService.exportFSRIS100Report(httpServletResponse, fsris100Request);
    }
}