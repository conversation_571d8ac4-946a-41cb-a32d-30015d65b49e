package tw.org.must.must.web.controller.list;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import tw.org.must.must.common.base.Page;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.Result;
import tw.org.must.must.core.service.list.*;
import tw.org.must.must.core.service.new_list.List2BasicFileBaseService;
import tw.org.must.must.core.service.new_list.List2BasicFileTemplateService;
import tw.org.must.must.core.service.new_list.List2FileQueueService;
import tw.org.must.must.core.service.sample.SampleTemplateInfoService;
import tw.org.must.must.core.shiro.LoginUtil;
import tw.org.must.must.dto.list.ListCopyInfoDto;
import tw.org.must.must.dto.list.ListFileQueueDto;
import tw.org.must.must.dto.list.List2FileQueueDeleteImpactDto;
import tw.org.must.must.model.list.*;
import tw.org.must.must.model.new_list.List2BasicFileBase;
import tw.org.must.must.model.new_list.List2FileQueue;
import tw.org.must.must.model.sample.SampleTemplateInfo;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "P2清单管理")
@RestController
@RequestMapping("/list2")
public class List2Controller {

    private static final Logger logger = LoggerFactory.getLogger(List2Controller.class);

    @Autowired
    private ListCategoryInfoService listCategoryInfoService;

    @Autowired
    private SampleTemplateInfoService sampleTemplateInfoService;

    @Autowired
    private ListCopyInfoService listCopyInfoService;

    @Autowired
    private List2FileQueueService listFileQueueService;

    @Autowired
    private ListCategoryService listCategoryService;

    @Autowired
    private List2BasicFileBaseService listBasicFileBaseService;

    @Autowired
    private List2BasicFileTemplateService listBasicFileTemplateService;

    @ApiOperation(value = "获取清单分类列表")
    @GetMapping("/category")
    public ResponseEntity saveListCategory(@RequestParam(value = "page_num", required = false, defaultValue = "0") Integer pageNum,
                                           @RequestParam(value = "page_size", required = false, defaultValue = "0") Integer pageSize) {
        List<ListCategoryInfo> list = listCategoryInfoService.listAllWithPage(pageNum, pageSize);
        return new ResponseEntity<>(new PageInfo<>(list), HttpStatus.OK);
    }

    @ApiOperation(value = "保存清单分类信息")
    @ApiImplicitParam(value = "清单分类信息", name = "listCategoryInfo", required = true, dataType = "ListCategoryInfo", paramType = "body")
    @PostMapping("/category")
    public ResponseEntity saveListCategory(@RequestBody ListCategoryInfo listCategoryInfo) {
        listCategoryInfoService.save(listCategoryInfo);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "保存样本模板信息")
    @ApiImplicitParam(value = "样本模板信息", name = "sampleTemplateInfo", required = true, dataType = "SampleTemplateInfo", paramType = "body")
    @PostMapping("/sample/template")
    public ResponseEntity addSampleTemplate(@RequestBody SampleTemplateInfo sampleTemplateInfo) {
        sampleTemplateInfoService.save(sampleTemplateInfo);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "获取全部样本模板信息")
    @GetMapping("/sample/template")
    public ResponseEntity addSampleTemplate() {
        List<SampleTemplateInfo> sampleTemplateInfos = sampleTemplateInfoService.listAll();
        return new ResponseEntity<>(sampleTemplateInfos, HttpStatus.OK);
    }

    @ApiOperation(value = "获取上传后新一般清单文件列表")
    @RequestMapping(value = "/getList2FileQueueList", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public Result<PageInfo<List2FileQueue>> getList2FileQueueList(@RequestBody ListFileQueueDto listFileQueueDto) {
        Page page = listFileQueueDto.getPage();
        if (null == page) {
            return new Result(HttpStatus.BAD_REQUEST.value(), "請求參數不正確！");
        }
//        PageHelper.startPage(page.getPageNum(), page.getPageSize());
//        List<ListFileQueue> list = listFileQueueService.getListFileQueueList(listFileQueueDto.getId(), listFileQueueDto.getCategoryCode(), listFileQueueDto.getFileName(),
//                listFileQueueDto.getFileUploadStartDate(), listFileQueueDto.getFileUploadEndDate(), listFileQueueDto.getUploadType(),
//                listFileQueueDto.getStatus(), listFileQueueDto.getFileType(), listFileQueueDto.getIsNoCategory(),  listFileQueueDto.getFilePath());
//        PageInfo<ListFileQueue> pageInfo = new PageInfo<>(list);


        PageInfo<List2FileQueue> pageInfo = listFileQueueService.getListFileQueueList2(listFileQueueDto.getPage(), listFileQueueDto.getId(), listFileQueueDto.getCategoryCode(), listFileQueueDto.getFileName(),
                listFileQueueDto.getFileUploadStartDate(), listFileQueueDto.getFileUploadEndDate(), listFileQueueDto.getUploadType(),
                listFileQueueDto.getStatus(), listFileQueueDto.getFileType(), listFileQueueDto.getIsNoCategory(),  listFileQueueDto.getFilePath(), listFileQueueDto.getUsageTime());


        return new Result(HttpStatus.OK.value(), null, pageInfo);
    }


    @ApiOperation(value = "查询删除新一般清单文件的影响数据(预览)")
    @GetMapping(value = "/deleteListFileQueue/{id}/preview")
    public Result<List2FileQueueDeleteImpactDto> previewDeleteList2FileQueue(@PathVariable("id") Long id) {
        if (id == null) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件队列ID不能为空", null);
        }

        try {
            List2FileQueueDeleteImpactDto impactDto = listFileQueueService.getDeleteImpact(id);
            return new Result<>(HttpStatus.OK.value(), "查询成功", impactDto);
        } catch (Exception e) {
            logger.error("查询删除影响失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "查询删除影响失败: " + e.getMessage(), null);
        }
    }

    @ApiOperation(value = "删除新一般清单文件数据(根据id)")
    @DeleteMapping(value = "/deleteListFileQueue/{id}")
    public Result<String> deleteList2FileQueue(@PathVariable("id") Long id) {
        listFileQueueService.deleteListFileQueueNew(id);
        return new Result(HttpStatus.OK.value(), null, "刪除成功！");
    }

    @ApiOperation(value = "删除新一般清单文件数据(批量)")
    @DeleteMapping(value = "/deleteListFileQueueList")
    public Result<String> deleteList2FileQueue(@RequestParam(value = "id") List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數id不能爲空！");
        }
        for (Long id : ids) {
            listFileQueueService.deleteListFileQueueNew(id);
        }
        return new Result(HttpStatus.OK.value(), null, "刪除成功！");
    }

    @ApiOperation(value = "新一般清单文件归档")
    @GetMapping(value = "/guidangList2FileQueueList")
    public Result<String> guidangList2FileQueueList(@RequestParam(value = "id") List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數id不能爲空！");
        }

        List<List2FileQueue> list = listFileQueueService.listByIds(ids);
        if(CollectionUtils.isEmpty(list)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件不存在！");
        }
        List<List2FileQueue> list2 = list.stream().filter(l -> !(l.getStatus() == 2 || l.getStatus() == 5)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(list2)){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "已歸檔的文件或者未完成的文件不能歸檔！");
        }

        List<List2FileQueue> updateList = new ArrayList<>();
        list.forEach(l -> {
            List2FileQueue lfq= new List2FileQueue();
            lfq.setStatus(4);
            lfq.setId(l.getId());
            l.setAmendTime(new Date());
            updateList.add(lfq);
        });

        listFileQueueService.updateBatchByPrimaryKeySelective(updateList);

        return new Result(HttpStatus.OK.value(), null, "歸檔成功！");
    }

    @ApiOperation(value = "新一般清单文件撤銷归档")
    @GetMapping(value = "/cancelGuidang2/{id}")
    public Result<String> cancelGuidang2(@PathVariable("id") Long id) {
        List2FileQueue listFileQueue = listFileQueueService.getById(id);
        if(null == listFileQueue){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件不存在！");
        }

        if(listFileQueue.getStatus() != 4){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "未歸檔的文件不需要撤銷！");
        }

        List2FileQueue update = new List2FileQueue();
        update.setId(id);
        update.setStatus(5);
        update.setAmendTime(new Date());


        listFileQueueService.updateSelective(update);

        return new Result(HttpStatus.OK.value(), null, "撤銷成功！");
    }
    @ApiOperation(value = "清单文件撤銷归档")
    @GetMapping(value = "/cancelGuidang/{id}")
    public Result<String> cancelGuidang(@PathVariable("id") Long id) {
        List2FileQueue listFileQueue = listFileQueueService.getById(id);
        if(null == listFileQueue){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件不存在！");
        }

        if(listFileQueue.getStatus() != 4){
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "未歸檔的文件不需要撤銷！");
        }

        List2FileQueue update = new List2FileQueue();
        update.setId(id);
        update.setStatus(5);
        update.setAmendTime(new Date());


        listFileQueueService.updateSelective(update);

        return new Result(HttpStatus.OK.value(), null, "撤銷成功！");
    }

//    @ApiOperation(value = "文件上传")
//    @PostMapping("/file/upload")
//    public ResponseEntity uploadFile(@RequestParam(value = "sampleId", required = false) Long sampleId,
//                                     @RequestParam("listTypeId") Long listTypeId,
//                                     @RequestParam("listTypeName") String listTypeName,
//                                     @RequestParam("fileType") String fileType,
//                                     @RequestParam("files") MultipartFile[] files) {
//        boolean result = listFileInfoPService.upload(sampleId, listTypeId, listTypeName, files, fileType);
//        if (result) {
//            return new ResponseEntity<>(HttpStatus.OK);
//        }
//        return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
//    }

    @ApiOperation(value = "文件上传")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "上传清单categoryCode", name = "categoryCode", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "文件类型，S-单场次，P-一般分配，P2-新一般清单，C-claim清单", name = "fileType", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "FW\\CJ\\MS\\PG\\CR  ，单场次选CR", name = "uploadType", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "优先级-值越大越优先处理", name = "sequence", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "Y:文件上传；N:目录上传", name = "isFile", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "目录上传时必填", name = "filePath", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "claimSetInfoId 当为claim的时候上传必填", required = false, name = "claimSetInfoId", dataType = "Long", paramType = "query"),
            @ApiImplicitParam(value = "文件", name = "files", dataType = "MultipartFile[]", paramType = "query")})
    @RequestMapping(value = "/uploadFile", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public Result<String> uploadFile(
            @RequestParam("categoryCode") String categoryCode,
            @RequestParam("fileType") String fileType,
            @RequestParam(value = "uploadType", required = false) String uploadType,
            @RequestParam(value = "sequence", required = false) String sequence,
            @RequestParam("isFile") String isFile,
            @RequestParam(required = false, name = "claimSetInfoId") Long claimSetInfoId,
            @RequestParam("filePath") String filePath,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "templateId", required = false) Long templateId,
            @RequestParam(required = false, value = "matchMark",defaultValue = "Y") String matchMark,
            @RequestParam("files") MultipartFile[] files) {
        logger.info("开始处理，文件【{}】，isfile【{}】", filePath, isFile);


        if (StringUtils.equalsIgnoreCase(fileType, "S")) {
            //单场次默认创建一个list-category code
            categoryCode = "單場次";
            uploadType = "FW"; // 以前CR是爲了區分單場次，現在不需要，默認都是FW
            ListCategory lc = listCategoryService.getCategoryByCode(categoryCode);
            if (null == lc) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件上傳失敗！categoryCode【單場次】不存在，請新建categroy后再重新上傳！");
            }
        }


        if ("Y".equalsIgnoreCase(isFile)) {


            if (StringUtils.isBlank(fileType)) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), "參數fileSource不存在！");
            }
            if (StringUtils.isNotBlank(categoryCode)) {
                ListCategory lc = listCategoryService.getCategoryByCode(categoryCode);
                if (null == lc) {
                    return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件上傳失敗！categoryCode不存在！");
                }
                if ("C".equalsIgnoreCase(fileType)) {
                    if (null == claimSetInfoId) {
                        return new Result<>(HttpStatus.BAD_REQUEST.value(), "claim上傳時，參數claimSetInfoId不存在！");
                    }
                }
            } else {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件上傳失敗！categoryCode不存在！");
            }

            // 根据文件类型选择不同的服务处理
            String result;
            if ("P2".equalsIgnoreCase(fileType)) {
                // P2类型使用List2FileQueueService处理
                List2FileQueue lfq2 = createList2FileQueue(categoryCode, fileType, uploadType, sequence, files, claimSetInfoId, startTime, endTime, templateId);
                lfq2.setMatchMark(matchMark);
                result = listFileQueueService.upload(lfq2, files);
            } else {
                // 其他类型使用原有的ListFileQueueService处理
                List2FileQueue lfq = createList2FileQueue(categoryCode, fileType, uploadType, sequence, files, claimSetInfoId, startTime, endTime, templateId);
                lfq.setMatchMark(matchMark);
                result = listFileQueueService.upload(lfq, files);
            }

            int code = HttpStatus.OK.value();
            if (StringUtils.isNotBlank(result)) {
                code = HttpStatus.INTERNAL_SERVER_ERROR.value();
            }
            return new Result<>(code, result);
        } else {
            // 目录上传   读取目录下所有的文件
            if (StringUtils.isNotBlank(filePath)) {
                String result = null;
                Map<String, Integer> msgResult = new HashMap<>();
                try {
                    // 根据文件类型选择不同的服务处理目录上传
                    if ("P2".equalsIgnoreCase(fileType)) {
                        result = listFileQueueService.upload(fileType, sequence, filePath, msgResult);
                    } else {
                        result = listFileQueueService.upload(fileType, sequence, filePath, msgResult);
                    }
                } catch (Exception e) {
                    result = "系統錯誤【" + e.getMessage() + "】，請聯係後台管理員！";
                    e.printStackTrace();
                }
                if (StringUtils.isBlank(result)) {
                    // 上传成功后 更新list_file_path_change 中status为0，表示下次
                    result = "文件上傳成功";
                    Integer succ = 0;
                    Integer err = 0;
                    if (msgResult.containsKey("succ")) {
                        succ = msgResult.get("succ");
                        result = String.format("%s，成功 %d", result, succ);

                    }
                    if (msgResult.containsKey("err")) {
                        err = msgResult.get("err");
                        result = String.format("%s，重複 %d", result, err);
                    }
                    if (succ == 0 && err > 0) {
                        return new Result<>(HttpStatus.BAD_REQUEST.value(), "該目錄已上傳過了，無需重複上傳！");
                    }
                    result = String.format("%s！", result);
                    return new Result<>(HttpStatus.OK.value(), result);
                } else {
                    return new Result<>(HttpStatus.BAD_REQUEST.value(), result);
                }
            } else {
                return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "文件上傳失敗，缺少文件目錄！");
            }
        }
    }

    @ApiOperation(value = "P2类型文件上传（新一般清单）")
    @ApiImplicitParams({
            @ApiImplicitParam(value = "上传清单categoryCode", name = "categoryCode", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "FW\\CJ\\MS\\PG\\CR  ，单场次选CR", name = "uploadType", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "优先级-值越大越优先处理", name = "sequence", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "Y:文件上传；N:目录上传", name = "isFile", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "目录上传时必填", name = "filePath", dataType = "String", paramType = "query"),
            @ApiImplicitParam(value = "文件", name = "files", dataType = "MultipartFile[]", paramType = "query")})
    @RequestMapping(value = "/uploadP2File", produces = "application/json;charset=utf-8", method = RequestMethod.POST)
    public Result<String> uploadP2File(
            @RequestParam("categoryCode") String categoryCode,
            @RequestParam(value = "uploadType", required = false) String uploadType,
            @RequestParam(value = "sequence", required = false) String sequence,
            @RequestParam("isFile") String isFile,
            @RequestParam("filePath") String filePath,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "endTime", required = false) String endTime,
            @RequestParam(value = "templateId", required = false) Long templateId,
            @RequestParam(required = false, value = "matchMark",defaultValue = "Y") String matchMark,
            @RequestParam("files") MultipartFile[] files) {

        logger.info("开始处理P2类型文件上传，文件【{}】，isfile【{}】", filePath, isFile);

        String fileType = "P2"; // 固定为P2类型

        // 验证categoryCode
        if (StringUtils.isBlank(categoryCode)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件上傳失敗！categoryCode不存在！");
        }

        ListCategory lc = listCategoryService.getCategoryByCode(categoryCode);
        if (null == lc) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "文件上傳失敗！categoryCode不存在！");
        }

        if ("Y".equalsIgnoreCase(isFile)) {
            // 文件上传
            List2FileQueue lfq2 = createList2FileQueue(categoryCode, fileType, uploadType, sequence, files, null, startTime, endTime, templateId);
            lfq2.setMatchMark(matchMark);
            String result = listFileQueueService.upload(lfq2, files);
            int code = HttpStatus.OK.value();
            if (StringUtils.isNotBlank(result)) {
                code = HttpStatus.INTERNAL_SERVER_ERROR.value();
            }
            return new Result<>(code, result);
        } else {
            // 目录上传
            if (StringUtils.isNotBlank(filePath)) {
                String result = null;
                Map<String, Integer> msgResult = new HashMap<>();
                try {
                    result = listFileQueueService.upload(fileType, sequence, filePath, msgResult);
                } catch (Exception e) {
                    result = "系統錯誤【" + e.getMessage() + "】，請聯係後台管理員！";
                    e.printStackTrace();
                }
                if (StringUtils.isBlank(result)) {
                    result = "文件上傳成功";
                    Integer succ = 0;
                    Integer err = 0;
                    if (msgResult.containsKey("succ")) {
                        succ = msgResult.get("succ");
                        result = String.format("%s，成功 %d", result, succ);
                    }
                    if (msgResult.containsKey("err")) {
                        err = msgResult.get("err");
                        result = String.format("%s，重複 %d", result, err);
                    }
                    if (succ == 0 && err > 0) {
                        return new Result<>(HttpStatus.BAD_REQUEST.value(), "該目錄已上傳過了，無需重複上傳！");
                    }
                    result = String.format("%s！", result);
                    return new Result<>(HttpStatus.OK.value(), result);
                } else {
                    return new Result<>(HttpStatus.BAD_REQUEST.value(), result);
                }
            } else {
                return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), "文件上傳失敗，缺少文件目錄！");
            }
        }
    }

    // http://10.0.0.71:8080/browse/MUST-1862
    @ApiOperation(value = "清单绑定categroyCode")
    @PostMapping(value = "/binding/category")
    public Result<String> bindingCategory(@RequestParam("categoryCode") String categoryCode, @RequestParam("id") Long id) {
        try {
            List2FileQueue fileQueue = listFileQueueService.getById(id);
            if (fileQueue == null) {
                return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("綁定失敗，找不到ID為【%d】的文件隊列記錄！", id));
            }
            List<List2BasicFileBase> listBasicFileBaseList = listBasicFileBaseService.getListBasicFileBaseList(fileQueue.getId(), null, null, null);
            Set<String> categoryCodeList = new HashSet<>();
            if (StringUtils.isNotBlank(fileQueue.getCategoryCode())) {
                if (fileQueue.getIsNoCategory() == 1 && !listCategoryService.checkListCategoryExistOrNot(fileQueue.getCategoryCode())) {
                    return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("綁定失敗，categoryCode【%s】不存在！", fileQueue.getCategoryCode()));
                }
                if (Objects.nonNull(listBasicFileBaseList) && !listBasicFileBaseList.isEmpty()) {
                    for (List2BasicFileBase lbfb : listBasicFileBaseList) {
                        if (listCategoryService.checkListCategoryExistOrNot(lbfb.getCategoryCode())) {
                            categoryCodeList.add(lbfb.getCategoryCode());
                        } else {
                            return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("綁定失敗，categoryCode【%s】不存在！", lbfb.getCategoryCode()));
                        }
                    }
                }
                fileQueue.setIsNoCategory(0);
            } else {
                fileQueue.setCategoryCode(categoryCode);
                fileQueue.setIsNoCategory(0);
                if (Objects.nonNull(listBasicFileBaseList) && !listBasicFileBaseList.isEmpty()) {
                    for (List2BasicFileBase lbfb : listBasicFileBaseList) {
                        String categoryCode2 = StringUtils.equalsAnyIgnoreCase(lbfb.getUploadType(), "CJ", "PG") ? String.format("%s-%s", categoryCode, lbfb.getUploadType()) : categoryCode;
                        ListCategory listCategory = listCategoryService.getCategoryByCode(categoryCode2);
                        if (listCategory != null) {
                            lbfb.setCategoryCode(categoryCode2);
                            lbfb.setPoolCode(listCategory.getPoolCode());
                            lbfb.setPoolRight(listCategory.getPoolRight());
                            lbfb.init();
                        } else {
                            return new Result<>(HttpStatus.BAD_REQUEST.value(), String.format("綁定失敗，categoryCode【%s】不存在！", categoryCode2));
                        }
                    }
                    listBasicFileBaseService.updateBatchByPrimaryKeySelective(listBasicFileBaseList);
                }
                //listFileQueue没有categoryCode, 是没有对应的模板， 现在需要找到对应的模板配置
                listBasicFileTemplateService.checkTemplate(fileQueue);
            }
            fileQueue.init();
            listFileQueueService.updateSelective(fileQueue);
           /* //绑定成功后，相同categoryCode的数据需要同步更新  已綁定過的不可修改
            List<ListFileQueue> listFileQueueList = listFileQueueService.getListFileQueueList(null, fileQueue.getCategoryCode(),
                    null, null, null, null, null, null, null, null);
            if (!listBasicFileBaseList.isEmpty()) {
                listFileQueueList = listFileQueueList.stream().filter(x -> Objects.nonNull(x.getIsNoCategory()) && x.getIsNoCategory() != 0).collect(Collectors.toList());
                for (ListFileQueue listFileQueue : listFileQueueList) {
                    List<ListBasicFileBase> bases = listBasicFileBaseService.getListBasicFileBaseList(listFileQueue.getId(), null, null, null);
                    if (Objects.nonNull(bases) && !bases.isEmpty()) {
                        Set<String> collect = bases.stream().map(ListBasicFileBase::getCategoryCode).collect(Collectors.toSet());
                        if (categoryCodeList.containsAll(collect)) {
                            listFileQueue.setIsNoCategory(0);
                            listFileQueue.init();
                            listFileQueueService.updateSelective(listFileQueue);
                        }
                    }
                }
            }*/
        } catch (MustException e) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), String.format("系统异常： %s", e.getMessage()));
        }
        return new Result<>(HttpStatus.OK.value(), "绑定成功");
    }


    @ApiOperation(value = "清单批量绑定categroyCode")
    @PostMapping(value = "/binding/categoryList")
    public Result<String> bindingCategoryList(@RequestParam("categoryCode") String categoryCode, @RequestParam("ids") List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "請求參數ID不能爲空！");
        }
        if(StringUtils.isBlank(categoryCode)) {
            return new Result<>(HttpStatus.BAD_REQUEST.value(), "categoryCode不能爲空！");
        }
        StringBuilder sb = new StringBuilder();
        List<List2FileQueue> queues = listFileQueueService.listByIds(ids);
        for (List2FileQueue fileQueue : queues) {
            try {
                if(!"P2".equals(fileQueue.getFileType())) {
                    continue; // 只能給單場次的綁定
                }
                boolean isBinded = true; // queue和base都有categoryCode, 就说明绑定过，不再绑定
                List<List2BasicFileBase> listBasicFileBaseList = listBasicFileBaseService.getListBasicFileBaseList(fileQueue.getId(), null, null, null);
                Set<String> categoryCodeList = new HashSet<>();
                if (StringUtils.isNotBlank(fileQueue.getCategoryCode())) {
                    if (fileQueue.getIsNoCategory() == 1 && !listCategoryService.checkListCategoryExistOrNot(fileQueue.getCategoryCode())) {
                        sb.append(String.format("id【%d】, 綁定失敗，categoryCode【%s】不存在！", fileQueue.getId(), fileQueue.getCategoryCode())).append("\n");
                        continue;
                    }
                    boolean isBind = true; // base数据 默认绑定成功
                    if (Objects.nonNull(listBasicFileBaseList) && !listBasicFileBaseList.isEmpty()) {
                        for (List2BasicFileBase lbfb : listBasicFileBaseList) {
                            String baseCategoryCode = lbfb.getCategoryCode();
                            if(StringUtils.isBlank(baseCategoryCode)) {
                                isBinded = false;
                                // 由于之前逻辑错误，或者其他原因，导致了queue有categoryCode，而base没有
                                // 现要补充base的categoryCode
                                String categoryCode2 = StringUtils.equalsAnyIgnoreCase(lbfb.getUploadType(), "CJ", "PG") ? String.format("%s-%s", categoryCode, lbfb.getUploadType()) : categoryCode;
                                ListCategory listCategory = listCategoryService.getCategoryByCode(categoryCode2);
                                if (listCategory != null) {
                                    lbfb.setCategoryCode(categoryCode2);
                                    lbfb.setPoolCode(listCategory.getPoolCode());
                                    lbfb.setPoolRight(listCategory.getPoolRight());
                                    lbfb.init();
                                } else {
                                    isBind = false;
                                    sb.append(String.format("id【%d】, 綁定失敗，categoryCode【%s】不存在！", fileQueue.getId(), categoryCode2)).append("\n");
                                    continue;
                                }
                            }
                        }
                    }
                    if(isBind) {
                        fileQueue.setCategoryCode(categoryCode);
                        fileQueue.setIsNoCategory(0);
                        listBasicFileBaseService.updateBatchByPrimaryKeySelective(listBasicFileBaseList);
                    }
                } else {
                    isBinded = false;
                    boolean isBind = true; // base数据 默认绑定成功
                    if (Objects.nonNull(listBasicFileBaseList) && !listBasicFileBaseList.isEmpty()) {
                        for (List2BasicFileBase lbfb : listBasicFileBaseList) {
                            String categoryCode2 = StringUtils.equalsAnyIgnoreCase(lbfb.getUploadType(), "CJ", "PG") ? String.format("%s-%s", categoryCode, lbfb.getUploadType()) : categoryCode;
                            ListCategory listCategory = listCategoryService.getCategoryByCode(categoryCode2);
                            if (listCategory != null) {
                                lbfb.setCategoryCode(categoryCode2);
                                lbfb.setPoolCode(listCategory.getPoolCode());
                                lbfb.setPoolRight(listCategory.getPoolRight());
                                lbfb.init();
                            } else {
                                isBind = false;
                                sb.append(String.format("id【%d】, 綁定失敗，categoryCode【%s】不存在！", fileQueue.getId(), categoryCode2)).append("\n");
                                continue;
                            }
                        }
                    }
                    if(isBind) {
                        fileQueue.setCategoryCode(categoryCode);
                        fileQueue.setIsNoCategory(0);
                        listBasicFileBaseService.updateBatchByPrimaryKeySelective(listBasicFileBaseList);
                    }
                    //listFileQueue没有categoryCode, 是没有对应的模板， 现在需要找到对应的模板配置
                    listBasicFileTemplateService.checkTemplate(fileQueue);
                }

                if(isBinded) {
                    //当前数据已经绑定过了，直接跳过
                    continue;
                }

                fileQueue.init();
                listFileQueueService.updateSelective(fileQueue);
                //绑定成功后，相同categoryCode的数据需要同步更新
                /*List<ListFileQueue> listFileQueueList = listFileQueueService.getListFileQueueList(null, fileQueue.getCategoryCode(),
                        null, null, null, null, null, null, null, null);
                if (!listBasicFileBaseList.isEmpty()) {
                    listFileQueueList = listFileQueueList.stream().filter(x -> Objects.nonNull(x.getIsNoCategory()) && x.getIsNoCategory() != 0).collect(Collectors.toList());
                    for (ListFileQueue listFileQueue : listFileQueueList) {
                        List<ListBasicFileBase> bases = listBasicFileBaseService.getListBasicFileBaseList(listFileQueue.getId(), null, null, null);
                        if (Objects.nonNull(bases) && !bases.isEmpty()) {
                            Set<String> collect = bases.stream().map(ListBasicFileBase::getCategoryCode).collect(Collectors.toSet());
                            if (categoryCodeList.containsAll(collect)) {
                                listFileQueue.setIsNoCategory(0);
                                listFileQueue.init();
                                listFileQueueService.updateSelective(listFileQueue);
                            }
                        }
                    }
                }*/
            } catch (MustException e) {
                e.printStackTrace();
                sb.append(String.format("id【%d】, 錯誤：", fileQueue.getId(), e.getMessage())).append("\n");
            } catch (Exception e) {
                e.printStackTrace();
                sb.append(String.format("id【%d】, 系统异常：：", fileQueue.getId(), e.getMessage())).append("\n");
            }
        }
        String message = sb.toString();
        if(StringUtils.isBlank(message)) {
            return new Result<>(HttpStatus.OK.value(), "绑定成功！");
        }else {
            return new Result<>(HttpStatus.INTERNAL_SERVER_ERROR.value(), message, null);
        }
    }


    private ListFileQueue createListFileQueue(String categoryCode, String fileType, String uploadType, String sequence,
                                              MultipartFile[] files, Long claimSetInfoId, String startTime, String endTime,
                                              Long templateId) {
        ListFileQueue lfq = new ListFileQueue();
        lfq.setAmendTime(new Date());
        lfq.setCreateTime(new Date());
        lfq.setFileType(fileType);
        lfq.setUploadType(uploadType);
        lfq.setSequence(StringUtils.isBlank(sequence) ? 1 : Integer.valueOf(sequence));
        lfq.setCategoryCode(categoryCode);
        lfq.setStatus(0);
        lfq.setUploadUserId(LoginUtil.getUserId());
        lfq.setUploadUserName(LoginUtil.getUserName());
        JSONObject object = new JSONObject();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
                lfq.setStartTime(simpleDateFormat.parse(startTime));
                lfq.setEndTime(simpleDateFormat.parse(endTime));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        object.put("claimSetInfoId", claimSetInfoId);
        object.put("templateId", templateId);
        String extJson = object.toJSONString();
        lfq.setExtJson(extJson);
        return lfq;
    }

    /**
     * 创建List2FileQueue对象，用于P2类型文件上传
     */
    private List2FileQueue createList2FileQueue(String categoryCode, String fileType, String uploadType, String sequence,
                                              MultipartFile[] files, Long claimSetInfoId, String startTime, String endTime,
                                              Long templateId) {
        List2FileQueue lfq = new List2FileQueue();
        lfq.setAmendTime(new Date());
        lfq.setCreateTime(new Date());
        lfq.setFileType(fileType);
        lfq.setUploadType(uploadType);
        lfq.setSequence(StringUtils.isBlank(sequence) ? 1 : Integer.valueOf(sequence));
        lfq.setCategoryCode(categoryCode);
        lfq.setStatus(0);
        lfq.setUploadUserId(LoginUtil.getUserId());
        lfq.setUploadUserName(LoginUtil.getUserName());
        JSONObject object = new JSONObject();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if(StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)){
                lfq.setStartTime(simpleDateFormat.parse(startTime));
                lfq.setEndTime(simpleDateFormat.parse(endTime));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }
        object.put("claimSetInfoId", claimSetInfoId);
        object.put("templateId", templateId);
        String extJson = object.toJSONString();
        lfq.setExtJson(extJson);
        return lfq;
    }

    @ApiOperation(value = "保存或者更新拉单信息")
    @ApiImplicitParam(value = "保存或者更新拉单信息", name = "listCopyInfoDto", required = true, dataType = "ListCopyInfoDto", paramType = "body")
    @PostMapping("/save/copy")
    public ResponseEntity<String> saveOrUpdateListCopyInfo(@RequestBody ListCopyInfoDto listCopyInfoDto) {
        listCopyInfoService.saveListCopy(listCopyInfoDto.getListCopyInfo(), listCopyInfoDto.getListCopyFilterList());
        return new ResponseEntity<String>("保存拉单信息成功！", HttpStatus.OK);
    }

    @ApiOperation(value = "获取拉单列表")
    @GetMapping("/copy/list")
    public ResponseEntity<PageInfo<ListCopyInfo>> getListCopyInfo(@RequestParam(value = "page_num", required = false, defaultValue = "0") Integer pageNum,
                                                                  @RequestParam(value = "page_size", required = false, defaultValue = "0") Integer pageSize) {
        List<ListCopyInfo> list = listCopyInfoService.listAllWithPage(pageNum, pageSize);
        PageInfo<ListCopyInfo> pageInfo = new PageInfo<ListCopyInfo>(list);
        return new ResponseEntity<>(pageInfo, HttpStatus.OK);
    }

    //    @ApiOperation(value = "绑定categoryCode")
//    @ApiImplicitParams({
//            @ApiImplicitParam(value = "上传清单categoryCode", name = "categoryCode", dataType = "String", paramType = "query"),
//            @ApiImplicitParam(value = "listFileQueue ID", name = "queueId", dataType = "Long", paramType = "query")})
//    @PostMapping("/bind/categorycode")
    public Result<String> bindCategoryCode(@RequestParam("categoryCode") String categoryCode, @RequestParam("queueId") Long queueId) {
        String message = "";
        Integer code = HttpStatus.BAD_REQUEST.value();
        if (StringUtils.isBlank(categoryCode) || Objects.isNull(queueId) || queueId < 1L) {
            message = "请求参数不能为空！";
        }
        if (!listCategoryService.checkListCategoryExistOrNot(categoryCode)) {
            message = String.format("categoryCode【%s】不存在！", categoryCode);
        }
        List2FileQueue listFileQueue = listFileQueueService.getById(queueId);
        if (Objects.isNull(listFileQueue)) {
            message = String.format("id【%d】对应的队列数据不存在！", queueId);
        }
        //从error迁移至done目录
        String filePath = listFileQueue.getFilePath();
        String newFilePath = filePath.replace("error", "done");
        File oldName = new File(filePath);
        File newFile = new File(newFilePath);
        File parentFile = newFile.getParentFile();
        if (!parentFile.exists()) {
            parentFile.mkdirs();
        }
        boolean flag = oldName.renameTo(newFile);
        listFileQueue.init();
        if (flag) {
            listFileQueue.setFilePath(newFilePath);
            listFileQueue.setFilePathMd5(DigestUtils.md5DigestAsHex(newFilePath.getBytes()));
            listFileQueue.setCategoryCode(categoryCode);
            listFileQueue.setStatus(0);
            listFileQueue.setDescription("");
            Integer integer = listFileQueueService.update(listFileQueue);
            if (integer == 1) {
                message = "绑定成功！";
                code = HttpStatus.OK.value();
            }
        } else {
            listFileQueue.setDescription("文件移动done目录出错！ " + listFileQueue.getDescription());
            listFileQueueService.update(listFileQueue);
            code = HttpStatus.INTERNAL_SERVER_ERROR.value();
            message = "文件移动done目录出错！";
        }
        return new Result<>(code, message);
    }
}

