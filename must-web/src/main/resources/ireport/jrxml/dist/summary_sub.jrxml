<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="large_fiche_rpt_subreport_otherTitle" pageWidth="595" pageHeight="842" columnWidth="595" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="fb506072-2bad-45c1-964e-a63c3177c298">
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="pool" class="java.lang.String"/>
	<field name="must" class="java.lang.String"/>
	<field name="mustShare" class="java.lang.String"/>
	<field name="overseas" class="java.lang.String"/>
	<field name="overseasShare" class="java.lang.String"/>
	<field name="sd" class="java.lang.String"/>
	<field name="sdShare" class="java.lang.String"/>
	<field name="summary" class="java.lang.String"/>
	<field name="summaryShare" class="java.lang.String"/>
	<background>
		<band height="1" splitType="Stretch"/>
	</background>
	<detail>
		<band height="100" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.FreeLayout"/>
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<line>
				<reportElement x="161" y="9" width="300" height="1" forecolor="#482AB5" uuid="45b6e515-4b99-4ea2-ac1e-f4f69dcbdb39">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="161" y="9" width="1" height="41" forecolor="#482AB5" uuid="0d2b7b91-cf6f-4122-8910-282e26ef9693">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="162" y="29" width="300" height="1" forecolor="#482AB5" uuid="a65b9854-84c3-4aa8-81e5-f715d35b820d">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="172" y="13" width="58" height="14" forecolor="#080008" uuid="457cbe67-0dd8-432f-b694-46324f74f26f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="华文宋体" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[MUST]]></text>
			</staticText>
			<line>
				<reportElement x="162" y="49" width="300" height="1" forecolor="#482AB5" uuid="7336e323-82c1-4883-8837-73306b7c26b0">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="162" y="69" width="300" height="1" forecolor="#482AB5" uuid="14ee3429-2819-47d9-b6c7-b0ae02170d32">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="171" y="33" width="58" height="14" forecolor="#080008" uuid="5aca3abd-d1d0-44f5-977b-932db50d95d5">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="华文宋体" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[OVERSEAS]]></text>
			</staticText>
			<staticText>
				<reportElement x="171" y="53" width="58" height="14" forecolor="#080008" uuid="0bc48436-403a-490c-af54-17e31ec483a7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="华文宋体" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[SD]]></text>
			</staticText>
			<line>
				<reportElement x="261" y="10" width="1" height="40" forecolor="#482AB5" uuid="413b2437-4898-4c89-b0e8-bfbc2e007f89">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="361" y="10" width="1" height="40" forecolor="#482AB5" uuid="8ff226d2-9bf8-413b-b48f-5391cccba2ef">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="461" y="9" width="1" height="41" forecolor="#482AB5" uuid="d8e1c4db-8d7e-41e9-b017-870baf295030">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="262" y="79" width="200" height="1" forecolor="#482AB5" uuid="e389eb82-a62f-4181-882f-0e2d8467e012">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="261" y="79" width="1" height="20" forecolor="#482AB5" uuid="e96b031d-58fb-4768-b0cc-6d2f90118c1d">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="361" y="79" width="1" height="20" forecolor="#482AB5" uuid="ed0f4c84-ec25-41aa-bb8b-74c8ea24c93e">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="461" y="79" width="1" height="20" forecolor="#482AB5" uuid="fc7825bd-29d9-42b3-8dde-b55ba7d5d098">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<staticText>
				<reportElement x="10" y="82" width="58" height="14" forecolor="#080008" uuid="864bf22b-90a2-406d-b32d-5c46341f4c7e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="华文宋体" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[總計]]></text>
			</staticText>
			<textField>
				<reportElement x="279" y="13" width="78" height="14" uuid="336322ca-ea05-4da3-ad1a-3143d00e4c1e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{must}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="379" y="82" width="78" height="14" uuid="345655c8-d539-449b-9cf3-d905998df981">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{summaryShare}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="379" y="13" width="78" height="14" uuid="694c0329-5f18-490b-b0bc-743625e318bf">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{mustShare}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="379" y="53" width="78" height="14" uuid="ae837c93-5fa9-425b-8899-711cffaccb64">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sdShare}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="379" y="33" width="78" height="14" uuid="43294f2d-3d52-47ab-b147-9830fee7ec04">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{overseasShare}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="279" y="82" width="78" height="14" uuid="cdaa65b5-4fdb-43d4-8161-b59f142641d2">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{summary}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="279" y="53" width="78" height="14" uuid="515769b4-a79a-4444-af68-4dcc9e5a895b">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sd}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="279" y="33" width="78" height="14" uuid="56e4bcc9-65d0-48cd-aa1d-76676bbed779">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<textElement textAlignment="Right">
					<font isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{overseas}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="161" y="50" width="1" height="20" forecolor="#482AB5" uuid="8b0da45f-4965-45ad-b2ce-7e6d5824f18b">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
			</line>
			<line>
				<reportElement x="261" y="49" width="1" height="20" forecolor="#482AB5" uuid="cbfe4453-638b-4188-8099-d26cf4852846">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
			</line>
			<line>
				<reportElement x="361" y="50" width="1" height="20" forecolor="#482AB5" uuid="878587f7-95f6-4493-9a23-959bc86ce5b5">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
			</line>
			<line>
				<reportElement x="461" y="49" width="1" height="20" forecolor="#482AB5" uuid="93ba91cd-58a0-4af9-973c-bf73e2d28298">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[null == $F{sd} || $F{sd}.equals("") ? false : true]]></printWhenExpression>
				</reportElement>
			</line>
			<textField textAdjust="StretchHeight">
				<reportElement x="10" y="13" width="110" height="14" uuid="53c03dec-b393-4771-be31-53a7c5637743">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Left">
					<font fontName="华文宋体" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pool}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="0" y="0" width="1" height="100" forecolor="#441DE0" uuid="17065f14-e5ba-4fec-ad5e-b32f39af95f6">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="485" y="0" width="1" height="100" forecolor="#482AB5" uuid="ad6adc54-e926-4e0c-b05a-8e0eae33b61c">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="99" width="486" height="1" isPrintInFirstWholeBand="true" forecolor="#482AB5" uuid="47e3aa48-d0e0-4443-a5c3-c44b9614fb7c">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="0" y="0" width="486" height="1" isPrintInFirstWholeBand="true" forecolor="#482AB5" uuid="bb1a4abe-f41c-466a-bd02-1d16f974a2ac">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
			</line>
		</band>
	</detail>
</jasperReport>
