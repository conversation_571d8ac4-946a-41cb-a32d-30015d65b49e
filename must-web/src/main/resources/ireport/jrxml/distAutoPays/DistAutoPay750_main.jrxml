<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.18.1.final using JasperReports Library version 6.18.1-9d75d1969e774d4f179fb3be8401e98a0e6d1611  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="DistAutoPay750_main" pageWidth="1000" pageHeight="842" columnWidth="1000" leftMargin="0" rightMargin="0" topMargin="0" bottomMargin="0" uuid="d327bde3-6dd2-4023-a2c2-ff3fe00c59cd">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="date" class="java.lang.String"/>
	<parameter name="reportId" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="count" class="java.lang.String"/>
	<parameter name="totalBalance" class="java.lang.String"/>
	<parameter name="SUBREPORT_DIR1" class="java.lang.String">
		<defaultValueExpression><![CDATA["ireport/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="autoPayNo" class="java.lang.String"/>
	<parameter name="distribution" class="java.lang.String"/>
	<parameter name="summaryTotal" class="java.lang.String"/>
	<parameter name="showDistNo" class="java.lang.Boolean">
		<defaultValueExpression><![CDATA[false]]></defaultValueExpression>
	</parameter>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="detailList" class="java.lang.Object"/>
	<field name="summaryTotal" class="java.lang.Object"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="48" splitType="Stretch"/>
	</title>
	<columnHeader>
		<band height="130" splitType="Stretch">
			<staticText>
				<reportElement x="340" y="0" width="400" height="20" uuid="f5735c0a-7fb2-4939-9b94-142838655ee1"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[MUSIC COPYRIGHT SOCIETY OF CHINESE TAIPEI 社團法人中華音樂著作權協會
]]></text>
			</staticText>
			<staticText>
				<reportElement x="250" y="30" width="550" height="20" uuid="6f42b9c4-7664-4291-96cb-ee640836ba80"/>
				<textElement>
					<font fontName="华文宋体" pdfFontName="STSong-Light" pdfEncoding="UniGB-UCS2-V"/>
				</textElement>
				<text><![CDATA[TAIWAN MEMBER AUTO-PAY (BY DISTRIBUTION NO) LIST 台灣會員自動轉賬(以分配代號排列)報表]]></text>
			</staticText>
			<staticText>
				<reportElement x="872" y="30" width="35" height="20" uuid="2d1f6570-b683-4273-a15d-ea3237080c6a">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<text><![CDATA[DATE : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="842" y="50" width="130" height="20" uuid="f3a40563-955a-4645-8c74-a836a7dd1b80">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<text><![CDATA[CURRENCY : NT$ 台幣]]></text>
			</staticText>
			<staticText>
				<reportElement x="845" y="70" width="130" height="20" uuid="f3a40563-955a-4645-8c74-a836a7dd1b80">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<text><![CDATA[REPORT ID : FSRIS750]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="68" width="75" height="20" uuid="57cc4a9c-1ef9-411a-9686-73ddedf809f9">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<text><![CDATA[AUTOPAY NO: ]]></text>
			</staticText>
			<staticText>
				<reportElement x="10" y="100" width="80" height="20" uuid="3dc75c72-cb0f-439d-a13c-a1819c68da84"/>
				<text><![CDATA[PA NAME NO.]]></text>
			</staticText>
			<staticText>
				<reportElement x="120" y="100" width="90" height="20" uuid="70b58330-bfb6-4fd6-b77a-dadd79d0fd55"/>
				<text><![CDATA[PA NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="100" width="90" height="20" uuid="3d46436d-760b-46bd-a85d-574796d80d6f">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<text><![CDATA[BALANCE]]></text>
			</staticText>
			<staticText>
				<reportElement x="420" y="100" width="120" height="20" uuid="9ea6e802-7c32-4c66-a7b4-840dfa1b6a45">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<text><![CDATA[BANK ACCOUNT NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="600" y="100" width="60" height="20" uuid="d29d7feb-2416-4f54-bc91-af2e6e0d33e5">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<text><![CDATA[BANK NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="740" y="100" width="60" height="20" uuid="fc1a4308-70f0-43e9-a06f-ae8fee66b0af"/>
				<text><![CDATA[BANK CODE]]></text>
			</staticText>
			<textField>
				<reportElement x="907" y="30" width="80" height="20" uuid="25b43def-54af-4904-af26-62f367612dd6"/>
				<textFieldExpression><![CDATA[$P{date}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="10" y="120" width="980" height="1" uuid="fcee8821-1f25-471f-bbe7-115aa581ff1c"/>
			</line>
			<textField>
				<reportElement x="85" y="68" width="80" height="20" uuid="5563a8b4-8bca-48a3-8cd6-46496e89d771">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{autoPayNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="175" y="68" width="75" height="20" uuid="421430e9-d291-429e-ac8d-19166c5a954a">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<text><![CDATA[DISTRIBUTION:]]></text>
			</staticText>
			<textField>
				<reportElement x="250" y="68" width="200" height="20" uuid="26d9c132-bfc5-44e0-b6c4-63e6262fd123">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textFieldExpression><![CDATA[$P{distribution}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="810" y="100" width="70" height="20" uuid="39530cc6-e719-4459-98c2-3d3f0db91df9"/>
				<text><![CDATA[BRANCH NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="890" y="100" width="100" height="20" uuid="5d49ad5b-df02-4af9-a9c2-fb06c96f2d08"/>
				<text><![CDATA[ACCOUNT NUMBER]]></text>
			</staticText>
			<image>
				<reportElement x="10" y="23" width="80" height="40" uuid="c32c57df-396c-425d-9fe3-59ab803b5a84">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<imageExpression><![CDATA["ireport/logo.png"]]></imageExpression>
			</image>
		</band>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<subreport>
				<reportElement x="10" y="0" width="980" height="20" uuid="b2846d08-6a5b-44b0-8800-5cb3d9abeb9f">
					<property name="com.jaspersoft.studio.unit.y" value="px"/>
				</reportElement>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("detailList")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPay750_sub.jasper")]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<pageFooter>
		<band height="48" splitType="Stretch">
			<textField evaluationTime="Report">
				<reportElement x="500" y="10" width="100" height="30" uuid="bf30c624-c578-47f3-a9d4-ed1f27dd3c14"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="10" width="100" height="30" uuid="f6b1ef4e-48a2-42a7-9dc8-85d313155410"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="189" splitType="Stretch">
			<staticText>
				<reportElement x="40" y="20" width="130" height="20" uuid="50d78344-fc78-49ae-b8bf-b19cb5f8afae"/>
				<text><![CDATA[TOTAL NO. OF MEMBERS:]]></text>
			</staticText>
			<line>
				<reportElement x="430" y="10" width="80" height="1" uuid="2560170a-0120-4391-bda8-55cc1f4a5a95">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<textField>
				<reportElement x="170" y="20" width="50" height="20" uuid="3d4ddbd2-2c56-428e-94fc-c064640e8a79"/>
				<textFieldExpression><![CDATA[$P{count}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="23" width="80" height="20" uuid="1798b7dc-a07b-4368-950e-b54ea6f9bfeb">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{totalBalance}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="400" y="50" width="110" height="1" uuid="ca7fce84-e400-4814-8361-7395b3292231">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="400" y="52" width="110" height="1" uuid="b2cd8657-c286-419a-8fd1-bd650b169da6">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
			</line>
			<line>
				<reportElement x="450" y="90" width="60" height="1" uuid="285db1ac-6629-4838-9d5f-1df4f8a72a46">
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{showDistNo} != null && $P{showDistNo}]]></printWhenExpression>
				</reportElement>
			</line>
			<subreport>
				<reportElement x="210" y="110" width="200" height="40" uuid="e73b83f0-41d1-496a-8c80-577d4739093a">
					<printWhenExpression><![CDATA[$P{showDistNo} != null && $P{showDistNo}]]></printWhenExpression>
				</reportElement>
				<dataSourceExpression><![CDATA[((net.sf.jasperreports.engine.data.JsonDataSource)$P{REPORT_DATA_SOURCE}).subDataSource("summaryTotal")]]></dataSourceExpression>
				<subreportExpression><![CDATA[Thread.currentThread().getContextClassLoader().getResource("ireport/jrxml/distAutoPays/DistAutoPay750_subTwo.jasper")]]></subreportExpression>
			</subreport>
			<textField>
				<reportElement x="425" y="60" width="88" height="20" uuid="213c7832-a550-44ab-b93a-4d40c301a580">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.x" value="px"/>
				</reportElement>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$P{summaryTotal}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement positionType="FixRelativeToBottom" x="390" y="160" width="120" height="21" uuid="aae4b529-8afb-4982-aa75-8979ea3064a6"/>
				<text><![CDATA[*** END OF REPORT ***]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
