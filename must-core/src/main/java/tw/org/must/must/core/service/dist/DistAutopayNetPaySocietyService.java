package tw.org.must.must.core.service.dist;


import net.sf.jasperreports.engine.JRException;
import tw.org.must.must.model.dist.DistAutopayNetPaySociety;
import tw.org.must.must.common.base.BaseService;
import tw.org.must.must.model.dist.vo.IncomeSocietyNetVo;

import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.List;

public interface DistAutopayNetPaySocietyService extends BaseService<DistAutopayNetPaySociety> {

    List<DistAutopayNetPaySociety> listDistAutopayNetPaySociety(Integer pageNum, Integer pageSize, String distNo, String societyCode);

    List<DistAutopayNetPaySociety> listDistAutopayNetPaySocietyForPay(Integer pageNum, Integer pageSize, String autopayNo, String distNo, String societyCode,Integer startYear,Integer endYear);
    IncomeSocietyNetVo listDistAutopayNetPaySociety(String autopayNo, String distNo, String societyCode, Integer startYear, Integer endYear, String societyName);

    Integer insertList(List<DistAutopayNetPaySociety> distAutopayNetPaySocietyList);

    List<DistAutopayNetPaySociety> getSocietyByExample(String autoPayNo, Integer societyCode, String distNo);

    List<DistAutopayNetPaySociety> getSocietyByExample(String autoPayNo, Integer societyCode, String distNo, String autopayDate);

    List<DistAutopayNetPaySociety> listDistAutopayNetPaySocietyForaffiliated(List<Integer> societyCodes);

    Integer redo(Long id);

    void reportDistAutoPaySoc800(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String societyCode, String distNo) throws FileNotFoundException, JRException;

    void reportDistAutoPaySoc780(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, Integer societyCode, String distNo, Boolean bankInfo, String autopayDate, Boolean groupBySociety) throws FileNotFoundException, JRException;

    Integer delete(String autopayNo);
}