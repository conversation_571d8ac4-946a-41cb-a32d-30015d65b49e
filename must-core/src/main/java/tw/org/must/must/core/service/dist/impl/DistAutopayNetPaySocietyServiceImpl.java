package tw.org.must.must.core.service.dist.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.util.BigDecimalUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.common.util.DateParse;
import tw.org.must.must.common.util.DateUtils;
import tw.org.must.must.common.util.ExampleUtil;
import tw.org.must.must.common.util.LocalCommonMethodUtils;
import tw.org.must.must.common.util.result.MustException;
import tw.org.must.must.common.util.result.ResultCode;
import tw.org.must.must.core.report.PdfDownloadUtil;
import tw.org.must.report.export.PdfReport;
import tw.org.must.must.core.service.export.impl.DistAutoNetPayExportServiceImpl;
import tw.org.must.must.mapper.dist.DistAutopaySocietyRetainMapper;
import tw.org.must.must.model.dist.DistAutopayNetPaySociety;
import tw.org.must.must.mapper.dist.DistAutopayNetPaySocietyMapper;
import tw.org.must.must.common.base.BaseServiceImpl;
import tw.org.must.must.core.service.dist.DistAutopayNetPaySocietyService;
import tw.org.must.must.model.dist.DistAutopaySocietyRetain;
import tw.org.must.must.model.dist.vo.IncomeSocietyNetVo;
import tw.org.must.must.model.dist.vo.v750.Bases750;
import tw.org.must.must.model.dist.vo.v750.DistAutoPay750Vo;
import tw.org.must.must.model.dist.vo.v800.*;
import tw.org.must.must.model.report.JasperParam;
import tw.org.must.must.model.report.distAutoPay.DistAutoPayOverseasReceipt;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Service
public class DistAutopayNetPaySocietyServiceImpl extends BaseServiceImpl<DistAutopayNetPaySociety> implements DistAutopayNetPaySocietyService {

    private static final Logger logger = LoggerFactory.getLogger(DistAutopayNetPaySocietyServiceImpl.class);

    private final DistAutopayNetPaySocietyMapper distAutopayNetPaySocietyMapper;
    private final DistAutopaySocietyRetainMapper distAutopaySocietyRetainMapper;
    @Lazy
    @Autowired
    private  DistAutoNetPayExportServiceImpl distAutoNetPayExportService;


    @Autowired
    public DistAutopayNetPaySocietyServiceImpl(DistAutopayNetPaySocietyMapper distAutopayNetPaySocietyMapper, DistAutopaySocietyRetainMapper distAutopaySocietyRetainMapper) {
        super(distAutopayNetPaySocietyMapper);
        this.distAutopayNetPaySocietyMapper = distAutopayNetPaySocietyMapper;
        this.distAutopaySocietyRetainMapper = distAutopaySocietyRetainMapper;
    }

    @Override
    public List<DistAutopayNetPaySociety> listDistAutopayNetPaySociety(Integer pageNum, Integer pageSize, String distNo, String societyCode) {
        PageHelper.startPage(pageNum, pageSize);
        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        if (StringUtils.isNotBlank(societyCode)) {
            criteria.andLike("societyCode", ExampleUtil.exampleLikeAll(societyCode));
        }
        criteria.andEqualTo("pay", "N");//未支付
        example.orderBy("createTime").desc();
        return distAutopayNetPaySocietyMapper.selectByExample(example);
    }

    @Override
    public List<DistAutopayNetPaySociety> listDistAutopayNetPaySocietyForPay(Integer pageNum, Integer pageSize, String autopayNo, String distNo, String societyCode, Integer startYear, Integer endYear) {
        PageHelper.startPage(pageNum, pageSize);
        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(autopayNo)) {
            criteria.andEqualTo("autopayNo", autopayNo);
        }
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        if (StringUtils.isNotBlank(societyCode)) {
            criteria.andLike("societyCode", ExampleUtil.exampleLikeAll(societyCode));
        }
        if (Objects.nonNull(startYear)) {
            criteria.andGreaterThanOrEqualTo("taxYear", startYear);
        }
        if (Objects.nonNull(endYear)) {
            criteria.andLessThanOrEqualTo("taxYear", endYear);
        }
        criteria.andEqualTo("pay", "Y");//已支付
        example.orderBy("createTime").desc();
        return distAutopayNetPaySocietyMapper.selectByExample(example);
    }

    @Override
    public IncomeSocietyNetVo listDistAutopayNetPaySociety(String autopayNo, String distNo, String societyCode, Integer startYear, Integer endYear,String societyName) {
        if (StringUtils.isBlank(societyCode) && StringUtils.isBlank(societyName) ) {
            throw new MustException(ResultCode.Unknown_Exception.getCode(), "soc no和name至少一个不能為空！");
        }
        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(autopayNo)) {
            criteria.andEqualTo("autopayNo", autopayNo);
        }
        if (StringUtils.isNotBlank(distNo)) {
            criteria.andLike("distNo", ExampleUtil.exampleLikeAll(distNo));
        }
        if (StringUtils.isNotBlank(societyCode)) {
            criteria.andLike("societyCode", ExampleUtil.exampleLikeAll(societyCode));
        }
        if (Objects.nonNull(startYear)) {
            criteria.andGreaterThanOrEqualTo("taxYear", startYear);
        }
        if (Objects.nonNull(endYear)) {
            criteria.andLessThanOrEqualTo("taxYear", endYear);
        }
        if (StringUtils.isNotBlank(societyName)) {
            criteria.andLike("societyName", ExampleUtil.exampleLikeAll(societyName));
        }
        criteria.andEqualTo("pay", "Y");//已支付
        example.orderBy("createTime").desc();
        List<DistAutopayNetPaySociety> distAutopayNetPaySocietyList = distAutopayNetPaySocietyMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(distAutopayNetPaySocietyList)) {
            return null;
        }
        IncomeSocietyNetVo incomeSocietyNetVo = new IncomeSocietyNetVo();
        incomeSocietyNetVo.setDistAutopayNetPaySocietyList(distAutopayNetPaySocietyList);
        //统计
        incomeSocietyNetVo.setAdvance(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getAdvance).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setPayAmount(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getPayAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setLocalTaxableIncome(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getLocalTaxableIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setOverseasTaxableIncome(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getOverseasTaxableIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setWithheldTax(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getWithheldTax).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setCommissionAmount(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getCommissionAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setNonTaxableIncome(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getNonTaxableIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
//        incomeSocietyNetVo.setSalesTaxAmount(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getSalesTaxAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO,BigDecimal::add));
        incomeSocietyNetVo.setRoyaltyAmount(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getRoyaltyAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        incomeSocietyNetVo.setDeduction(distAutopayNetPaySocietyList.stream().map(DistAutopayNetPaySociety::getDeduction).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));

        return incomeSocietyNetVo;
    }

    @Override
    public Integer insertList(List<DistAutopayNetPaySociety> distAutopayNetPaySocietyList) {
        if (CollectionUtils.isEmpty(distAutopayNetPaySocietyList)) {
            return null;
        }
        return distAutopayNetPaySocietyMapper.insertList(distAutopayNetPaySocietyList);
    }

    @Override
    public List<DistAutopayNetPaySociety> getSocietyByExample(String autoPayNo, Integer societyCode, String distNo) {
        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("autopayNo", autoPayNo);
        if (societyCode != null) {
            criteria.andEqualTo("societyCode", societyCode);
        }
        if(StringUtils.isNotBlank(distNo)){
            criteria.andEqualTo("distNo", distNo);
        }
        return distAutopayNetPaySocietyMapper.selectByExample(example);
    }

    @Override
    public List<DistAutopayNetPaySociety> getSocietyByExample(String autoPayNo, Integer societyCode, String distNo, String autopayDate) {
        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("autopayNo", autoPayNo);
        if (societyCode != null) {
            criteria.andEqualTo("societyCode", societyCode);
        }
        if(StringUtils.isNotBlank(distNo)){
            criteria.andEqualTo("distNo", distNo);
        }
        if(StringUtils.isNotBlank(autopayDate)){
            try {
                Date payDate = DateParse.parseDate(autopayDate);
                criteria.andEqualTo("payDate", payDate);
            } catch (Exception e) {
                // 如果日期解析失败，记录日志但不影响查询
                logger.warn("Failed to parse autopayDate: {}", autopayDate, e);
            }
        }
        return distAutopayNetPaySocietyMapper.selectByExample(example);
    }

    @Override
    public List<DistAutopayNetPaySociety> listDistAutopayNetPaySocietyForaffiliated(List<Integer> societyCodes) {
        if (CollectionUtils.isEmpty(societyCodes)) {
            return null;
        }
        return distAutopayNetPaySocietyMapper.selectBySocietyCodes(societyCodes);
    }

    @Override
    public Integer redo(Long id) {
        DistAutopayNetPaySociety distAutopayNetPaySociety = distAutopayNetPaySocietyMapper.selectByPrimaryKey(id);
        DistAutopaySocietyRetain distAutopaySocietyRetain = new DistAutopaySocietyRetain();
//        BeanUtils.copyProperties(distAutopayNetPaySociety, distAutopaySocietyRetain);
//        distAutopaySocietyRetain.setId(null);
//        distAutopaySocietyRetain.init();
        distAutopaySocietyRetain.setPay("Y");

        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id",distAutopayNetPaySociety.getRetainId());
        distAutopaySocietyRetainMapper.updateByExampleSelective(distAutopaySocietyRetain,example);
        return distAutopayNetPaySocietyMapper.delete(distAutopayNetPaySociety);
    }

    @Override
    public void reportDistAutoPaySoc800(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String societyCode, String distNo) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPaySocietyServiceImpl.class.getResource("/ireport").getPath();
        Data800 data800 = sealDistAutoPay800Data(autopayNo, societyCode, distNo);
        Map<String,Object> param = new HashMap<>();
        param.put("date", data800.getDate());
        param.put("reportId", data800.getReportId());
        param.put("autoPayNo", autopayNo);
        param.put("distribution", autopayDescription);
        param.put("count", data800.getCount());
        param.put("total", data800.getTotal());
        param.put("paymentTotal", data800.getPaymentTotal());
        param.put("taxAmount", data800.getTaxAmount());
        param.put("commissionAmount", data800.getCommissionAmount());
        param.put("reciprocalAmount", data800.getReciprocalAmount());

        param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
        param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

        //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
        List<JasperParam> jaspers = new ArrayList<>();
        jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(data800.getBases800()).getBytes()));

        //调用生成pdf方法
        PdfDownloadUtil.download(jaspers,  "/ireport/jrxml/distAutoPays/DistAutoPaySociety800_main.jrxml", httpServletResponse, autopayNo);
    }

    private Data800 sealDistAutoPay800Data(String autopayNo, String societyCode, String distNo) {
        Data800 data800 = new Data800();
        Bases800 bases800 = new Bases800();
        List<DistNoDetail800> distNoDetail800TotalList = new ArrayList<>();
        List<Detail800> detail800List = distAutopayNetPaySocietyMapper.getDetail800(autopayNo, societyCode, distNo);
        if (CollectionUtils.isEmpty(detail800List)){
            throw new MustException(ResultCode.Unknown_Exception.getCode(),"無可導出數據!");
        }
        //detailList
        AtomicInteger payAbleTo = new AtomicInteger(1);
        detail800List.forEach(detail800 -> {
            List<DistNoDetail800> distNoDetail800List = distAutopayNetPaySocietyMapper.getDistNoDetail800(autopayNo, societyCode, distNo);
            BigDecimal royalties = distNoDetail800List.stream().
                    map(distNoDetail800 -> distNoDetail800.getORoyalties() == null ? BigDecimal.ZERO : new BigDecimal(distNoDetail800.getORoyalties())).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            detail800.setRoyalties(royalties.toString());
            BigDecimal payAbleAmount = distNoDetail800List.stream().
                    map(distNoDetail800 -> distNoDetail800.getOPayAbleAmount() == null ? BigDecimal.ZERO : new BigDecimal(distNoDetail800.getOPayAbleAmount())).
                    reduce(BigDecimal.ZERO, BigDecimal::add);
            detail800.setPayAbleAmount(payAbleAmount.toString());
            distNoDetail800List.forEach(distNoDetail800 -> distNoDetail800.setPayAbleTo(String.format("%03d", payAbleTo.getAndIncrement())));
            detail800.setDistNoLists(distNoDetail800List);
            distNoDetail800TotalList.addAll(distNoDetail800List);
        });
        bases800.setDetailList(detail800List);
        //summaryTotal
        Map<String, BigDecimal> summaryTotalMap = distNoDetail800TotalList.stream().
                collect(Collectors.groupingBy(DistNoDetail800::getDistNo,
                        Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getRoyaltiesA, BigDecimal::add)));
        if (MapUtils.isNotEmpty(summaryTotalMap)) {
            List<Summary800> summary800List = new ArrayList<>();
            summaryTotalMap.forEach((k, v) -> {
                Summary800 summary800 = new Summary800();
                summary800.setDistNo(k);
                summary800.setTotalAmount(v == null ? null : v.toString());
                summary800List.add(summary800);
            });
            bases800.setSummaryTotal(summary800List);
        }
        data800.setBases800(bases800);
        //data800其他数据
        data800.setDate(DateUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm"));
        data800.setReportId("ERDIS800");
        data800.setAutoPayNo(autopayNo);
        data800.setCount(String.valueOf(detail800List.size()));
        //计算各金额总和
        BigDecimal paymentTotal = distNoDetail800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getPayA, BigDecimal::add));
        BigDecimal taxAmount = distNoDetail800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getTaxA, BigDecimal::add));
        BigDecimal commissionAmount = distNoDetail800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getCommissionA, BigDecimal::add));
        BigDecimal reciprocalAmount = distNoDetail800TotalList.stream().collect(Collectors.reducing(BigDecimal.ZERO, DistNoDetail800::getReciprocalA, BigDecimal::add));
        data800.setPaymentTotal(paymentTotal.toString());
        data800.setTaxAmount(taxAmount.toString());
        data800.setCommissionAmount(commissionAmount.toString());
        data800.setReciprocalAmount(reciprocalAmount.toString());
        data800.setTotal(paymentTotal.add(taxAmount).add(commissionAmount).add(reciprocalAmount).toString());
        return data800;
    }

    @Override
    public void reportDistAutoPaySoc780(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, Integer societyCode, String distNo, Boolean bankInfo, String autopayDate, Boolean groupBySociety) throws FileNotFoundException, JRException {
        String JRXML_PATH = DistAutopayNetPaySocietyServiceImpl.class.getResource("/ireport").getPath();
        List<DistAutoPayOverseasReceipt> distAutoPayOverseasReceiptList = distAutoNetPayExportService.exportOverseasReceipt(autopayNo, bankInfo,societyCode, distNo);

        if (groupBySociety != null && groupBySociety) {
            // 按协会分组，每个协会生成一个PDF文件
            generatePdfsBySociety(httpServletResponse, autopayNo, autopayDescription, autopayDate, distAutoPayOverseasReceiptList, JRXML_PATH);
        } else {
            // 原有逻辑：所有数据合并到一个PDF文件
            generateSinglePdf(httpServletResponse, autopayNo, autopayDescription, autopayDate, distAutoPayOverseasReceiptList, JRXML_PATH);
        }
    }

    /**
     * 生成单个PDF文件（原有逻辑）
     */
    private void generateSinglePdf(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String autopayDate, List<DistAutoPayOverseasReceipt> distAutoPayOverseasReceiptList, String JRXML_PATH) throws FileNotFoundException, JRException {
        //此处是测试蒋两个pdf合并在一起，如果只数据一个pdf的，则传入一个JasperParam即可
        List<JasperParam> jaspers = new ArrayList<>();
        distAutoPayOverseasReceiptList.forEach(distAutoPayOverseasReceipt -> {
            Map<String,Object> param = new HashMap<>();
            // 如果传入了autopayDate，使用传入的日期，否则使用原有的日期
            param.put("date", StringUtils.isNotBlank(autopayDate) ? autopayDate : distAutoPayOverseasReceipt.getDate());
            param.put("societyName",distAutoPayOverseasReceipt.getSocietyName());
            param.put("societyCode", String.format("%03d",distAutoPayOverseasReceipt.getSocietyCode()));
            param.put("payCurrency",distAutoPayOverseasReceipt.getPayCurrency());
            param.put("feeInError", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getFeeInError()));
            param.put("adjTotalAmount", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getAdjTotalAmount()));
            param.put("adjSendAmount", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getAdjSendAmount()));
            param.put("paymentMethod", distAutoPayOverseasReceipt.getPaymentMethod());
            param.put("affiliated", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getAffiliated()));
            param.put("bankCharge", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getBankCharge()));
            param.put("netPayment", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getNetPayment()));
            param.put("exchangeRate", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getExchangeRate()));
            param.put("totalPayment", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getTotalPayment()));
            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");
            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(distAutoPayOverseasReceipt.getBases()).getBytes()));
        });
        //调用生成pdf方法
        PdfDownloadUtil.download(jaspers,   "/ireport/jrxml/distAutoPays/DistAutoPay780_main.jrxml", httpServletResponse, autopayNo);
    }

    /**
     * 按协会分组生成PDF文件（每个协会生成独立的PDF文件）
     */
    private void generatePdfsBySociety(HttpServletResponse httpServletResponse, String autopayNo, String autopayDescription, String autopayDate, List<DistAutoPayOverseasReceipt> distAutoPayOverseasReceiptList, String JRXML_PATH) throws FileNotFoundException, JRException {
        // 按协会代码分组，保持顺序
        Map<Integer, List<DistAutoPayOverseasReceipt>> societyGroupMap = distAutoPayOverseasReceiptList.stream()
                .collect(Collectors.groupingBy(DistAutoPayOverseasReceipt::getSocietyCode, LinkedHashMap::new, Collectors.toList()));

        // 创建临时目录存放PDF文件
        String tempDir = System.getProperty("java.io.tmpdir") + File.separator + "society_pdfs_" + System.currentTimeMillis();
        File tempDirFile = new File(tempDir);
        if (!tempDirFile.exists()) {
            tempDirFile.mkdirs();
        }

        List<File> pdfFiles = new ArrayList<>();

        try {
            // 为每个协会生成独立的PDF文件
            for (Map.Entry<Integer, List<DistAutoPayOverseasReceipt>> entry : societyGroupMap.entrySet()) {
                Integer societyCode = entry.getKey();
                List<DistAutoPayOverseasReceipt> receipts = entry.getValue();

                // 获取协会名称（从第一个receipt中获取）
                String societyName = receipts.get(0).getSocietyName();

                // 生成单个协会的PDF文件
                String pdfFileName = tempDir + File.separator + autopayNo + "_" + String.format("%03d", societyCode) + "_" + societyName + ".pdf";
                generateSingleSocietyPdf(pdfFileName, receipts, autopayDate, JRXML_PATH);

                pdfFiles.add(new File(pdfFileName));
            }

            // 将所有PDF文件打包成ZIP并返回
            createZipWithPdfs(httpServletResponse, pdfFiles, autopayNo + "_按协会分组");

        } catch (Exception e) {
            throw new RuntimeException("生成协会PDF文件失败", e);
        } finally {
            // 清理临时文件
            for (File pdfFile : pdfFiles) {
                if (pdfFile.exists()) {
                    pdfFile.delete();
                }
            }
            // 删除临时目录
            if (tempDirFile.exists()) {
                tempDirFile.delete();
            }
        }
    }

    @Override
    public Integer delete(String autopayNo) {
        Example example = new Example(DistAutopayNetPaySociety.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("autopayNo", autopayNo);
        criteria.andEqualTo("pay", "Y");//已支付
        example.orderBy("createTime").desc();
        List<DistAutopayNetPaySociety> distAutopayNetPaySocieties = distAutopayNetPaySocietyMapper.selectByExample(example);
        for(DistAutopayNetPaySociety d : distAutopayNetPaySocieties){
            redo(d.getId());
        }
        return distAutopayNetPaySocieties.size();
    }

    /**
     * 为单个协会生成PDF文件
     */
    private void generateSingleSocietyPdf(String pdfFileName, List<DistAutoPayOverseasReceipt> receipts, String autopayDate, String JRXML_PATH) throws Exception {
        List<JasperParam> jaspers = new ArrayList<>();

        for (DistAutoPayOverseasReceipt distAutoPayOverseasReceipt : receipts) {
            Map<String,Object> param = new HashMap<>();
            // 如果传入了autopayDate，使用传入的日期，否则使用原有的日期
            param.put("date", StringUtils.isNotBlank(autopayDate) ? autopayDate : distAutoPayOverseasReceipt.getDate());
            param.put("societyName", distAutoPayOverseasReceipt.getSocietyName());
            param.put("societyCode", String.format("%03d", distAutoPayOverseasReceipt.getSocietyCode()));
            param.put("payCurrency", distAutoPayOverseasReceipt.getPayCurrency());
            param.put("feeInError", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getFeeInError()));
            param.put("adjTotalAmount", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getAdjTotalAmount()));
            param.put("adjSendAmount", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getAdjSendAmount()));
            param.put("paymentMethod", distAutoPayOverseasReceipt.getPaymentMethod());
            param.put("affiliated", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getAffiliated()));
            param.put("bankCharge", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getBankCharge()));
            param.put("netPayment", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getNetPayment()));
            param.put("exchangeRate", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getExchangeRate()));
            param.put("totalPayment", LocalCommonMethodUtils.formate(distAutoPayOverseasReceipt.getTotalPayment()));
            param.put("SUBREPORT_DIR", JRXML_PATH + "/jrxml/");
            param.put("SUBREPORT_DIR1", JRXML_PATH + "/jrxml/");

            jaspers.add(new JasperParam(param, LocalCommonMethodUtils.toString(distAutoPayOverseasReceipt.getBases()).getBytes()));
        }

        // 生成PDF文件到指定路径
        PdfReport.exprtPdf(pdfFileName, jaspers, "/ireport/jrxml/distAutoPays/DistAutoPay780_main.jrxml");
    }

    /**
     * 将多个PDF文件打包成ZIP并通过HttpServletResponse返回
     */
    private void createZipWithPdfs(HttpServletResponse httpServletResponse, List<File> pdfFiles, String zipFileName) throws IOException {
        // 设置响应头
        httpServletResponse.setContentType("application/zip");
        httpServletResponse.setCharacterEncoding("utf-8");
        String encodedFileName = URLEncoder.encode(zipFileName + ".zip", StandardCharsets.UTF_8);
        httpServletResponse.addHeader("Content-Disposition", "attachment;filename=\"" + encodedFileName + "\"");

        try (ZipOutputStream zipOut = new ZipOutputStream(httpServletResponse.getOutputStream())) {
            for (File pdfFile : pdfFiles) {
                if (pdfFile.exists()) {
                    // 添加文件到ZIP
                    ZipEntry zipEntry = new ZipEntry(pdfFile.getName());
                    zipOut.putNextEntry(zipEntry);

                    try (FileInputStream fis = new FileInputStream(pdfFile)) {
                        byte[] buffer = new byte[8192];
                        int length;
                        while ((length = fis.read(buffer)) > 0) {
                            zipOut.write(buffer, 0, length);
                        }
                    }

                    zipOut.closeEntry();
                }
            }
            zipOut.flush();
        }
    }
}