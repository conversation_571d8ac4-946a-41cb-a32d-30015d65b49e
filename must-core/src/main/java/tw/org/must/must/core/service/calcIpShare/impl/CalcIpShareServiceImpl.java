package tw.org.must.must.core.service.calcIpShare.impl;

import com.alibaba.fastjson.JSON;
import io.swagger.models.auth.In;
import net.sf.cglib.beans.BeanMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tw.org.must.must.common.collectors.CollectorsUtil;
import tw.org.must.must.common.constants.Constants;
import tw.org.must.must.common.util.DateUtils;
import tw.org.must.must.core.handle.redisService.RedisService;
import tw.org.must.must.core.redis.NoGener;
import tw.org.must.must.core.service.agr.AgrAssigneeService;
import tw.org.must.must.core.service.agr.AgrAssignorService;
import tw.org.must.must.core.service.agr.AgrContentService;
import tw.org.must.must.core.service.calcIpShare.CalcIpShareService;
import tw.org.must.must.core.service.mbr.*;
import tw.org.must.must.core.service.ref.RefRightService;
import tw.org.must.must.core.service.ref.RefRoleConvertService;
import tw.org.must.must.core.service.ref.RefTerritoryRelationService;
import tw.org.must.must.core.service.wrk.WrkWorkIpShareService;
import tw.org.must.must.core.service.wrk.WrkWorkRightService;
import tw.org.must.must.core.service.wrk.WrkWorkService;
import tw.org.must.must.model.agr.AgrAssignee;
import tw.org.must.must.model.agr.AgrContentVO;
import tw.org.must.must.model.mbr.*;
import tw.org.must.must.model.ref.RefRight;
import tw.org.must.must.model.ref.RefRoleConvert;
import tw.org.must.must.model.ref.RefTerritoryRelation;
import tw.org.must.must.model.util.TreeToolUtil;
import tw.org.must.must.model.wrk.WrkWork;
import tw.org.must.must.model.wrk.WrkWorkIpShare;
import tw.org.must.must.model.wrk.WrkWorkIpShareTree;
import tw.org.must.must.model.wrk.WrkWorkRight;
import tw.org.must.must.model.wrk.ipshare.WrkWorkIpShareVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CalcIpShareServiceImpl implements CalcIpShareService {

    private Logger logger = LoggerFactory.getLogger(CalcIpShareServiceImpl.class);
    @Autowired
    private WrkWorkIpShareService wrkWorkIpShareService;

    @Autowired
    private MbrIpNameService mbrIpNameService;

    @Autowired
    private MbrIpNameMergeService mbrIpNameMergeService;

    @Autowired
    private MbrIpService mbrIpService;

    @Autowired
    private RefRightService refRightService;

    @Autowired
    private MbrIpAgreementService mbrIpAgreementService;

    @Autowired
    private RefTerritoryRelationService refTerritoryRelationService;

    @Autowired
    private MbrIpAgreementTerritoryService mbrIpAgreementTerritoryService;

    @Autowired
    private AgrAssignorService agrAssignorService;

    @Autowired
    private AgrContentService agrContentService;

    @Autowired
    private AgrAssigneeService agrAssigneeService;

    @Autowired
    private WrkWorkRightService wrkWorkRightService;

    @Autowired
    private RedisService redisService;

    @Lazy
    @Autowired
    private WrkWorkService workService;

    @Autowired
    private MbrMemberMembershipService mbrMemberMembershipService;

    private ConcurrentHashMap<String,List<AgrContentVO>> concurrentHashMap = new ConcurrentHashMap<>();

    public void test(){
        String workUniqueKey = "026-1223831306";
        String year = "2025";
        String rightType = "PER";
        List<String> rightTypeList = new ArrayList<>();
        rightTypeList.add(rightType);
        WrkWork wrkWork = new WrkWork();
        wrkWork.setWorkUniqueKey(workUniqueKey);
        List<WrkWorkIpShare> workIpShareList = calcWorkIpShare(wrkWork, year, rightTypeList);
        for (WrkWorkIpShare wrkWorkIpShare : workIpShareList) {
            logger.debug("当前workIpRole : "+wrkWorkIpShare.getWorkIpRole()+";当前ipshare:"+wrkWorkIpShare.getIpShare()+";当前分组："+wrkWorkIpShare.getGroupIndicator()+";当前权利类型："+wrkWorkIpShare.getRightType()+"；当前ipNameNo:"+wrkWorkIpShare.getIpNameNo());
        }
    }

    @Override
    public List<WrkWorkIpShare> calcWorkIpShare(WrkWork wrkWork, String year, String queryRightType, String calcRightType) {

        return calcWorkIpShare(wrkWork,year,queryRightType,calcRightType,false) ;
    }

    @Override
    public List<WrkWorkIpShare> calcWorkIpShare(WrkWork wrkWork, String year, String queryRightType, String calcRightType, Boolean isDist) {
        if(StringUtils.equals(queryRightType,calcRightType)){

            List<String> rightTypes = new ArrayList<>();
            rightTypes.add(queryRightType);
            return calcWorkIpShare(wrkWork,year,rightTypes,isDist) ;

        } else {
            concurrentHashMap.clear();
            String workUniqueKey = wrkWork.getWorkUniqueKey();

            List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey = wrkWorkIpShareService.getWrkWorkIpShareByWorkUniqueKey(workUniqueKey,queryRightType);

            if(null == wrkWorkIpShareByWorkUniqueKey || wrkWorkIpShareByWorkUniqueKey.size()<1){
                logger.info("作品"+workUniqueKey+"下无ipShare~");
                return new ArrayList<>();
            }

            wrkWorkIpShareByWorkUniqueKey.forEach(w -> w.setRightType(calcRightType));

            return calcWorkIpShareForTwoType(wrkWork,year,wrkWorkIpShareByWorkUniqueKey,isDist) ;
        }
    }

    public List<WrkWorkIpShare> calcWorkIpShareForTwoType(WrkWork wrkWork, String year, List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey, Boolean isDist) {
        List<String> workTypes = Arrays.asList("ADP","ARR") ;
        if(StringUtils.isBlank(year)){
            year = new SimpleDateFormat("yyyy").format(new Date());
        }

        if(workTypes.contains(wrkWork.getWorkType()) ){
            return calcWorkIpShareWithADP(wrkWork, wrkWorkIpShareByWorkUniqueKey,year,isDist) ;
        }else{
            return calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist) ;
        }
    }

    @Override
    public List<WrkWorkIpShare> calcWorkIpShare(WrkWork wrkWork, String year, List<String> rightTypeList) {
        return calcWorkIpShare(wrkWork,year,rightTypeList,false) ;
    }

    @Override
    public List<WrkWorkIpShare> calcWorkIpShare(WrkWork wrkWork, String year, List<String> rightTypeList, Boolean isDist) {
        concurrentHashMap.clear();
        String workUniqueKey = wrkWork.getWorkUniqueKey();

        List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey = wrkWorkIpShareService.getWrkWorkIpShareByWorkUniqueKey(workUniqueKey,rightTypeList);

        if(null == wrkWorkIpShareByWorkUniqueKey || wrkWorkIpShareByWorkUniqueKey.size()<1){
            logger.info("作品"+workUniqueKey+"下无ipShare~");
            return new ArrayList<>();
        }

        List<String> workTypes = Arrays.asList("ADP","ARR") ;
        if(StringUtils.isBlank(year)){
            year = new SimpleDateFormat("yyyy").format(new Date());
        }

        if(workTypes.contains(wrkWork.getWorkType()) ){
            return calcWorkIpShareWithADP(wrkWork, wrkWorkIpShareByWorkUniqueKey,year,isDist) ;
        }else{
            return calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist) ;
        }
    }

    private List<WrkWorkIpShare> calcWorkIpShareWithADP(WrkWork wrkWork,List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey, String year, Boolean isDist){


//        List<String> ipRoles = Arrays.asList("SA", "AR","TR","AD") ;

        List<WrkWorkIpShare> results = new ArrayList<>() ;

//        List<String> rightTypeList = Arrays.asList("PER") ;
        Map<String,List<WrkWorkIpShare>> groupByRightTypeMap = wrkWorkIpShareByWorkUniqueKey.stream().collect(Collectors.groupingBy(WrkWorkIpShare :: getRightType));

        List<WrkWorkRight> wrkWorkRightList = wrkWorkRightService.getWrkWorkRightByWorkUnique(wrkWork.getWorkUniqueKey()) ;
        Map<String,List<WrkWorkRight>> wrkWorkRightMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(wrkWorkRightList)){
            wrkWorkRightMap = wrkWorkRightList.stream().collect(Collectors.groupingBy(WrkWorkRight::getRightType));
        }
        for(Map.Entry<String,List<WrkWorkIpShare>> entry : groupByRightTypeMap.entrySet()){
            if(!wrkWorkRightMap.isEmpty() && wrkWorkRightMap.containsKey(Constants.rightTypeMap.get(entry.getKey()))){
                WrkWorkRight wrkWorkRight = wrkWorkRightMap.get(Constants.rightTypeMap.get(entry.getKey())).get(0);
                String shareType = wrkWorkRight.getShareType();
                if(shareType.equals("M")){
                    results.addAll(calcWorkIpShareWithADPManual(wrkWork,entry.getValue(), year, isDist,entry.getKey()));
                } else {
                    results.addAll(calcWorkIpShareWithADPAuto(wrkWork,entry.getValue(), year,isDist,entry.getKey()) );
                }
            }

        }

        return results;
    }

    private List<WrkWorkIpShare> calcWorkIpShareWithADPAuto(WrkWork wrkWork,List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey, String year, Boolean isDist, String rightType){


        List<String> eOrSe = Arrays.asList("E","SE") ;
        List<String> rightTypeList = new ArrayList<>() ;
        rightTypeList.add(Constants.rightTypeMap.get(rightType));

        List<WrkWorkIpShare> list = wrkWorkIpShareByWorkUniqueKey.stream().filter(it -> (Constants.WORK_IP_ROLE_UPDATE.contains(it.getWorkIpRole()))).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        //获取原曲
        WrkWork originWrkWork = workService.getWrkWorkByWorkId(wrkWork.getRefWorkId(),wrkWork.getRefWorkSociety());
        if(null == originWrkWork) {
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        List<WrkWorkIpShare> results = new ArrayList<>() ;

        //获取原曲ipshare
        List<WrkWorkIpShare> originWrkWorkIpShare = wrkWorkIpShareService.getWrkWorkIpShareByWorkUniqueKey(originWrkWork.getWorkUniqueKey(),rightTypeList);
        originWrkWorkIpShare.forEach(it->it.setRightType(rightType));
        if(CollectionUtils.isEmpty(originWrkWorkIpShare)){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        //计算原曲ipshare
        List<WrkWorkIpShare> calcOriginWrkWorkIpShare = calcWorkIpShare(originWrkWork,originWrkWorkIpShare,year,isDist);

        List<WrkWorkIpShare> saWrkWorkIpShare = wrkWorkIpShareByWorkUniqueKey.stream().filter(w -> StringUtils.equals(w.getRefIndicator(),"N")).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(saWrkWorkIpShare)){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        //分给SA的总份额 shareType = A(Auto) 默认33.33 如果ipshare.workIpRole含有E或者SE 此比例为16.66，如果有多个SA 则按此比例平分，即比例(33.33或16.66)/count(SA)
        //             shareType = M(Manual) 取页面手动输入值，多个SA取和
        BigDecimal saIpshareTotal = BigDecimal.valueOf(33.33);

        BigDecimal sumc = BigDecimal.ZERO, suma = BigDecimal.ZERO, multiWeight ,half = BigDecimal.valueOf(0.5);
        Map<String, List<WrkWorkIpShare>> initGpMap = calcOriginWrkWorkIpShare.stream().filter(it ->
                (StringUtils.isNotBlank(it.getGroupIndicator()))).collect(Collectors.groupingBy(it -> it.getGroupIndicator()));

        for(Map.Entry<String, List<WrkWorkIpShare>> initGp : initGpMap.entrySet()){  // 第一个for 汇总 A C
            List<WrkWorkIpShare> ipShares = initGp.getValue() ;
            boolean hasEOrSe = ipShares.stream().anyMatch(ip -> eOrSe.contains(ip.getWorkIpRole()));
            if(hasEOrSe){
                saIpshareTotal = BigDecimal.valueOf(16.66) ; //ipshare任意一个GP存在E或SE 分配给SA的比例修改为16.66
                multiWeight = BigDecimal.ONE;
            } else {
                multiWeight = half ; // 计算权重的时候 如果当前分组没有E或SE 原曲比例拆分成一半再计算权重
            }
            for(WrkWorkIpShare ipShare :ipShares){
                BigDecimal weight = ipShare.getIpShare();
                if(weight.compareTo(BigDecimal.ZERO) == 0){
                    continue;
                }
                weight = weight.multiply(multiWeight);
                switch (ipShare.getWorkIpRole()){
                    case "CA" :
                        BigDecimal ca = weight.multiply(half) ; // CA 拆成一半A一半C计算
                        suma = suma.add(ca);
                        sumc = sumc.add(ca);
                        ipShare.setIpShareWeightc(ca);
                        ipShare.setIpShareWeighta(ca);
                        break;
                    case "C" :
                        sumc = sumc.add(weight);
                        ipShare.setIpShareWeighta(BigDecimal.ZERO);
                        ipShare.setIpShareWeightc(weight);
                        break;
                    case "A" :
                        suma = suma.add(weight);
                        ipShare.setIpShareWeighta(weight);
                        ipShare.setIpShareWeightc(BigDecimal.ZERO);
                        break;
                }
            }

        }

        if(saIpshareTotal.compareTo(suma.add(sumc)) == 1){ //分给SA的总比例 > A + C 暂不分配 否则比例可能为负数
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        // C A C/A分给SA的比例 如果suma、sumc均 > 0,SA份额由c、a平分, saIpshareFromAC=saIpshareTotal/2 ,否则全部由c.a中不为0的分
        BigDecimal saIpshareFromAC = saIpshareTotal;
        if(suma.compareTo(BigDecimal.ZERO) == 1 && sumc.compareTo(BigDecimal.ZERO) == 1){
            saIpshareFromAC = saIpshareTotal.divide(BigDecimal.valueOf(2),6, RoundingMode.HALF_UP) ;
        }

        // 第二个for 根据上面的汇总 计算分配给SA的比例 原比例-分给SA比例=最终比例
        // saIpshareFromAC * a / suma、 saIpshareFromAC * c / sumc、 C和A分别分给SA的比例
        // 原比例 - 分出去的比例 = 最终比例 即： ipshare - saIpshareFromAC * a / suma - saIpshareFromAC * c / sumc = 最终比例
        for(WrkWorkIpShare originipShare : calcOriginWrkWorkIpShare){
            if(Constants.WORK_IP_ROLE_ORIGNIAL.contains(originipShare.getWorkIpRole())){
                BigDecimal caIpshare =  originipShare.getIpShare() ;
                BigDecimal ipShareWeighta = originipShare.getIpShareWeighta();
                if(ipShareWeighta != null && ipShareWeighta.compareTo(BigDecimal.ZERO) == 1){ // 如果ipShareWeighta > 0 ====> suma > 0 ,ipShareWeightc > 0 ====> sumc > 0，可以除
                    ipShareWeighta = saIpshareFromAC.multiply(ipShareWeighta).divide(suma,6,RoundingMode.HALF_UP) ;
                    caIpshare = caIpshare.subtract(ipShareWeighta);
                }

                BigDecimal ipShareWeightc = originipShare.getIpShareWeightc();
                if(ipShareWeightc != null && ipShareWeightc.compareTo(BigDecimal.ZERO) == 1){
                    ipShareWeightc = saIpshareFromAC.multiply(ipShareWeightc).divide(sumc,6,RoundingMode.HALF_UP) ;
                    caIpshare = caIpshare.subtract(ipShareWeightc);
                }

                originipShare.setIpShare(caIpshare);
                originipShare.setOrgWriterShare(originipShare.getIpShare());
                originipShare.setOrignalWrkIpShare(originipShare.getIpShare());
            }

            results.add(originipShare);
        }

        results = createWrkWorkIpShare(results,wrkWork);

        //每个sa分配到的比例
        long saCount = saWrkWorkIpShare.size();
        BigDecimal saIpshare = saIpshareTotal.divide(BigDecimal.valueOf(saCount),6, RoundingMode.HALF_UP) ;
        for(WrkWorkIpShare sa : saWrkWorkIpShare){
            if(sa.getWorkIpRole().equals("SA")){
                sa.setIpShare(saIpshare);
            }
        }

        List<WrkWorkIpShare> calcSaWrkWorkIpShare = calcWorkIpShare(wrkWork,saWrkWorkIpShare,year,isDist);

        results.addAll(calcSaWrkWorkIpShare) ;

        WrkWorkRight wrkWorkRight = wrkWorkRightService.getWrkWorkRightByWorkUniqueAndRightType(wrkWork.getWorkUniqueKey(),rightTypeList.get(0)) ;
        if(wrkWorkRight != null){
            if(wrkWorkRight.getWorkSd().equals("Y")){
                calcOriginWrkWorkIpShare.forEach(c -> c.setSd("Y"));
                saWrkWorkIpShare.forEach(c -> c.setSd("Y"));
            }
        }

        return results;
    }

    private List<WrkWorkIpShare> calcWorkIpShareWithADPManual(WrkWork wrkWork,List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey, String year, Boolean isDist,String rightType){


        List<String> rightTypeList = new ArrayList<>() ;
        rightTypeList.add(Constants.rightTypeMap.get(rightType));

        List<WrkWorkIpShare> list = wrkWorkIpShareByWorkUniqueKey.stream().filter(it -> (Constants.WORK_IP_ROLE_UPDATE.contains(it.getWorkIpRole()))).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(list)){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        //获取原曲
        WrkWork originWrkWork = workService.getWrkWorkByWorkId(wrkWork.getRefWorkId(),wrkWork.getRefWorkSociety());
        if(null == originWrkWork) {
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        List<WrkWorkIpShare> results = new ArrayList<>() ;

        //获取原曲ipshare
        List<WrkWorkIpShare> originWrkWorkIpShare = wrkWorkIpShareService.getWrkWorkIpShareByWorkUniqueKey(originWrkWork.getWorkUniqueKey(),rightTypeList);
        originWrkWorkIpShare.forEach(it->it.setRightType(rightType));
        if(CollectionUtils.isEmpty(originWrkWorkIpShare)){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

//        originWrkWorkIpShare.addAll(saWrkWorkIpShare);
        //计算原曲ipshare
//        List<WrkWorkIpShare> calcOriginWrkWorkIpShare = calcWorkIpShare(originWrkWork,originWrkWorkIpShare,year);

        originWrkWorkIpShare.forEach(w -> {if(w.getIpShare() == null) w.setIpShare(BigDecimal.ZERO);});

        // 补充GP
        List<WrkWorkIpShare> fillGpWrkWorkIpShareList = fillGp(originWrkWorkIpShare);

        // SA补充GP
        List<WrkWorkIpShare> fillGpWrkWorkIpShareListADP = fillGp(wrkWorkIpShareByWorkUniqueKey);

        // 原曲回归比例
        List<WrkWorkIpShare> workIpShareList = wrkWorkIpShareService.recoveryWorkIpShare(fillGpWrkWorkIpShareList);

        // ADP回归比例
        List<WrkWorkIpShare> workIpShareListADP = wrkWorkIpShareService.recoveryWorkIpShare(fillGpWrkWorkIpShareListADP);

        Map<String,WrkWorkIpShare> refWrkWorkIpShareMap = workIpShareList.stream().collect(Collectors.toMap(ori ->  ori.getGroupIndicator() + (ori.getIpNameNo()==null ? "0":ori.getIpNameNo()) + (ori.getWorkIpRole()== null?"0":ori.getWorkIpRole()),Function.identity(),(a,b) ->a));
        workIpShareList.forEach(s -> s.setIpsharem(BigDecimal.ZERO));
        //        Map<String,WrkWorkIpShare> adpWrkWorkIpShareMap = fillGpWrkWorkIpShareList.stream().collect(Collectors.toMap(adp ->  adp.getGroupIndicator() + (adp.getIpNameNo()==null ? "0":adp.getIpNameNo()) + (adp.getWorkIpRole()== null?"0":adp.getWorkIpRole()),Function.identity(),(a,b) ->a));

        List<WrkWorkIpShare> saWrkWorkIpShare = new ArrayList<>();
        for(WrkWorkIpShare sa : workIpShareListADP){
            String s1 = sa.getGroupIndicator() + (sa.getIpNameNo()==null ? "0":sa.getIpNameNo() ) + (sa.getWorkIpRole()== null?"0":sa.getWorkIpRole());
            WrkWorkIpShare ref = refWrkWorkIpShareMap.get(s1) ;
            if(sa.getIpShare() == null){
                sa.setIpShare(BigDecimal.ZERO);
            }
            if(sa.getRefIndicator().equals("N")){
                saWrkWorkIpShare.add(sa);
                sa.setIpsharem(sa.getIpShare());
            } else if(ref != null){
                ref.setIpsharem(sa.getIpShare());
            }
        }

        if(CollectionUtils.isEmpty(saWrkWorkIpShare)){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        // 原曲创建树结构
        List<WrkWorkIpShareTree> wrkWorkIpShareTreeList = createWrkWorkIpShareTreeList(originWrkWork.getGenre(),originWrkWork.getWorkUniqueKey(),workIpShareList, year,originWrkWork.getRefWorkId(),originWrkWork.getRefWorkSociety(),isDist);

        if(null == wrkWorkIpShareTreeList || wrkWorkIpShareTreeList.size()<1){
            return  calcWorkIpShare(wrkWork,wrkWorkIpShareByWorkUniqueKey,year,isDist);
        }

        List<WrkWorkIpShareTree> resultWrkWorkIpShareList = removeHasAgrNoPersonWrite(wrkWorkIpShareTreeList,workIpShareList);

        // 计算  ipshare
        for (WrkWorkIpShareTree wrkWorkIpShareTree : resultWrkWorkIpShareList) {
            calcIpShareADP(wrkWorkIpShareTree);
        }

        List<WrkWorkIpShareTree> treeToWrkWorkIpShare = new TreeToolUtil().getTreeToWrkWorkIpShare(resultWrkWorkIpShareList);

//        List<WrkWorkIpShareTree> treeToWrkWorkIpShare = parseWrkWorkIpShareTree(wrkWorkIpShareTreeList);

        // 公共財產處理
        dealCommonProperty(treeToWrkWorkIpShare, year);
        if(null == treeToWrkWorkIpShare|| treeToWrkWorkIpShare.size()<1){
            return  calcWorkIpShare(wrkWork,fillGpWrkWorkIpShareListADP,year,isDist);
        }

        List<WrkWorkIpShare> result = changeTreeToIpShare(treeToWrkWorkIpShare);

        // 排序
        List<WrkWorkIpShare> sortByWorkIpRole = sortByWorkIpRole(result);
        sortByWorkIpRole.forEach(s -> {
            s.setWorkId(wrkWork.getWorkId());
            s.setWorkSocietyCode(wrkWork.getWorkSocietyCode());
            s.setWorkUniqueKey(wrkWork.getWorkUniqueKey());
            s.setRefIndicator("Y");
        });
        results.addAll(sortByWorkIpRole);

        List<WrkWorkIpShare> sortByWorkIpRoleSa = calcWorkIpShare(wrkWork,saWrkWorkIpShare,year,isDist);
        results.addAll(sortByWorkIpRoleSa);

        WrkWorkRight wrkWorkRight = wrkWorkRightService.getWrkWorkRightByWorkUniqueAndRightType(wrkWork.getWorkUniqueKey(),rightTypeList.get(0)) ;
        if(wrkWorkRight != null){
            if(wrkWorkRight.getWorkSd().equals("Y")){
                results.forEach(c -> c.setSd("Y"));
            }
        }

        results.forEach(ip -> {
            if(ip.getIpsharem() != null ){
                ip.setOrignalWrkIpShare(ip.getIpsharem());
                ip.setOrgWriterShare(ip.getIpsharem());
            } else {
                ip.setOrignalWrkIpShare(ip.getIpsharem());
                ip.setOrgWriterShare(ip.getIpsharem());
            }
        });

        logger.info("最终结果>>>>>>>>>>>>>>"+"\r\n");
        results.forEach(it ->{
            logger.info("当前gp为："+it.getGroupIndicator()+";当前的ipshare为："+it.getIpShare()+";\r\n"
                    +"当前的workIpRole ： "+it.getWorkIpRole()+"；当前的ipNameNo : "+it.getIpNameNo()+"当前作品workWorkUnique : "+it.getWorkUniqueKey());
        });
        return results;

    }

    @Override
    public List<WrkWorkIpShare> calcWorkIpShare(WrkWork wrkWork,List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey,String year,Boolean isDist) {
        String workUniqueKey = wrkWork.getWorkUniqueKey();
        Long refWorkId = wrkWork.getRefWorkId();
        Integer refWorkSociety = wrkWork.getRefWorkSociety();
        String genre = wrkWork.getGenre();

        wrkWorkIpShareByWorkUniqueKey.forEach(w -> {if(w.getIpShare() == null) w.setIpShare(BigDecimal.ZERO);});

        // 补充GP
        List<WrkWorkIpShare> fillGpWrkWorkIpShareList = fillGp(wrkWorkIpShareByWorkUniqueKey);

        // 回归比例
        List<WrkWorkIpShare> workIpShareList = wrkWorkIpShareService.recoveryWorkIpShare(fillGpWrkWorkIpShareList);

        // 先创建树结构
        List<WrkWorkIpShareTree> wrkWorkIpShareTreeList = createWrkWorkIpShareTreeList(genre,workUniqueKey,workIpShareList, year,refWorkId,refWorkSociety,isDist);

        if(null == wrkWorkIpShareTreeList || wrkWorkIpShareTreeList.size()<1){
            logger.info("作品"+workUniqueKey+"下无ipShare~");
            return new ArrayList<>();
        }

        // 查看树结构下是否手动填写的E
//        List<WrkWorkIpShareTree> personWrite = wrkWorkIpShareTreeList.stream().filter(it -> ("E".equalsIgnoreCase(it.getWorkIpRole()) || "PA".equalsIgnoreCase(it.getWorkIpRole())) && 0 == it.getOipLinkId()).collect(Collectors.toList());

        // 计算前去除掉当前组下有合约同时存在手动填写的E
        List<WrkWorkIpShareTree> resultWrkWorkIpShareList = removeHasAgrNoPersonWrite(wrkWorkIpShareTreeList,workIpShareList);

        // 计算  ipshare
        for (WrkWorkIpShareTree wrkWorkIpShareTree : resultWrkWorkIpShareList) {
            calcIpShare(wrkWorkIpShareTree);
        }

        // 计算后去除掉手动填写的E  这里的E是和手动填写的同一个
//       List<WrkWorkIpShareTree> resultWrkWorkIpShareList = removePersonWrite(wrkWorkIpShareTreeList,personWrite);

//        if(null == resultWrkWorkIpShareList|| resultWrkWorkIpShareList.size()<1){
//            logger.info("作品"+workUniqueKey+"在去除手動填寫E后無IP share！");
//            return new ArrayList<>();
//        }

        List<WrkWorkIpShareTree> treeToWrkWorkIpShare = new TreeToolUtil().getTreeToWrkWorkIpShare(resultWrkWorkIpShareList);

//        List<WrkWorkIpShareTree> treeToWrkWorkIpShare = parseWrkWorkIpShareTree(wrkWorkIpShareTreeList);

        // 公共財產處理
        dealCommonProperty(treeToWrkWorkIpShare, year);
        if(null == treeToWrkWorkIpShare|| treeToWrkWorkIpShare.size()<1){
            logger.info("作品"+workUniqueKey+"在公共財產處理后無IP share！");
            return new ArrayList<>();
        }

        List<WrkWorkIpShare> result = changeTreeToIpShare(treeToWrkWorkIpShare);

        // 排序
        List<WrkWorkIpShare> sortByWorkIpRole = sortByWorkIpRole(result);

        logger.info("最终结果>>>>>>>>>>>>>>"+"\r\n");
        sortByWorkIpRole.forEach(it ->{
            logger.info("当前gp为："+it.getGroupIndicator()+";当前的ipshare为："+it.getIpShare()+";\r\n"
                    +"当前的workIpRole ： "+it.getWorkIpRole()+"；当前的ipNameNo : "+it.getIpNameNo()+"当前作品workWorkUnique : "+it.getWorkUniqueKey());
        });

        return sortByWorkIpRole;
    }


    public List<WrkWorkIpShare> setOriginIpShare(List<WrkWorkIpShare> wrkWorkIpShareList){
        List<WrkWorkIpShare> recoveryWorkIpShareList = wrkWorkIpShareList.stream()
                .filter(it -> it != null && it.getOipLinkId() != null && it.getOipLinkId() != 0 && it.getIpShare() != null && it.getIpShare().compareTo(BigDecimal.ZERO) == 1)
                .collect(Collectors.toList());
        if(org.apache.commons.collections.CollectionUtils.isEmpty(recoveryWorkIpShareList)){
            return wrkWorkIpShareList;
        }

        Map<Long,WrkWorkIpShare> map = wrkWorkIpShareList.stream().collect(Collectors.toMap(WrkWorkIpShare :: getSipLinkId,  Function.identity(), (key1, key2) -> key2));
        for(WrkWorkIpShare workIpShareO : recoveryWorkIpShareList){
            Long sipLinkId = workIpShareO.getOipLinkId();
            BigDecimal ipshare = BigDecimal.ZERO;
            while(true){
                WrkWorkIpShare workIpShareI = map.get(sipLinkId) ;
                if(workIpShareI != null){
                    if(workIpShareI.getIpShare().compareTo(BigDecimal.ZERO) == 1){
                        ipshare = ipshare.add(workIpShareI.getIpShare());
                    }
                    if(workIpShareI.getOipLinkId() == null || workIpShareI.getOipLinkId() == 0){
                        workIpShareI.setOrgWriterShare(ipshare);
                        workIpShareI.setOrignalWrkIpShare(ipshare);
                        break;
                    }
                    sipLinkId = workIpShareI.getOipLinkId();
                }
                break;
            }
        }

        return map.values().stream().collect(Collectors.toList());
    }


    @Override
    public List<WrkWorkIpShare> calcWorkIpShare(String workUniqueKey) {
        if(StringUtils.isBlank(workUniqueKey)) {
            return new ArrayList<>();
        }
        WrkWork wrkWork = workService.getWrkWorkByWorkUniqueKey(workUniqueKey);
        if(null == wrkWork) {
            return new ArrayList<>();
        }
        return calcWorkIpShare(wrkWork, null, new ArrayList<>(Arrays.asList("PER")));
    }

    private List<WrkWorkIpShareTree> parseWrkWorkIpShareTree(List<WrkWorkIpShareTree> wrkWorkIpShareTreeList) {

        List<WrkWorkIpShareTree> resultWrkWorkIpShare = new ArrayList<>();

        parseChildrenWorkIpShare(wrkWorkIpShareTreeList, resultWrkWorkIpShare);
        return resultWrkWorkIpShare;
    }

    private void parseChildrenWorkIpShare(List<WrkWorkIpShareTree> wrkWorkIpShareTreeList, List<WrkWorkIpShareTree> resultWrkWorkIpShare) {
        for (WrkWorkIpShareTree wrkWorkIpShareTree : wrkWorkIpShareTreeList) {
            boolean repeat = wrkWorkIpShareTree.isRepeat();
            if(repeat){
                continue;
            }
            boolean hasBody = wrkWorkIpShareTree.isHasBody();
            if(hasBody){
                List<WrkWorkIpShareTree> workIpShareList = wrkWorkIpShareTree.getWorkIpShareList();
                parseChildrenWorkIpShare(workIpShareList,resultWrkWorkIpShare);
            }
            resultWrkWorkIpShare.add(wrkWorkIpShareTree);
        }
    }

    private List<WrkWorkIpShareTree> removeHasAgrNoPersonWrite(List<WrkWorkIpShareTree> wrkWorkIpShareTreeList,List<WrkWorkIpShare> workIpShareList) {

        List<WrkWorkIpShareTree> resultWrkWorkIpshare = new ArrayList<>();
        resultWrkWorkIpshare.addAll(wrkWorkIpShareTreeList);

        Map<String, List<WrkWorkIpShare>> initGpAndRightTypeMap = workIpShareList.stream().filter(it -> StringUtils.isNotBlank(it.getGroupIndicator())).collect(Collectors.groupingBy(it -> it.getGroupIndicator() + it.getRightType()));

        Map<String,Boolean> isNeedDelete = new HashMap<>();

        Map<String,Long> remainWrkWorkIpShareMap = new HashMap<>();

        for (WrkWorkIpShare wrkWorkIpShare : workIpShareList) {
            String workIpRole1 = wrkWorkIpShare.getWorkIpRole();
            if(!Constants.WORK_IP_ROLE_ORIGNIAL.contains(workIpRole1)){
                continue;
            }

            String groupIndicator1 = wrkWorkIpShare.getGroupIndicator();
            String rightType1 = wrkWorkIpShare.getRightType();
            String ipNameNo1 = wrkWorkIpShare.getIpNameNo();

            String key1 = workIpRole1+groupIndicator1+rightType1+ipNameNo1;

            String mapKey = groupIndicator1+rightType1;

            for (WrkWorkIpShareTree wrkWorkIpShareTree : wrkWorkIpShareTreeList) {
                String groupIndicator = wrkWorkIpShareTree.getGroupIndicator();
                String rightType = wrkWorkIpShareTree.getRightType();
                String workIpRole = wrkWorkIpShareTree.getWorkIpRole();
                String ipNameNo = wrkWorkIpShareTree.getIpNameNo();

                String key = workIpRole+groupIndicator+rightType+ipNameNo;

                if(key1.equalsIgnoreCase(key)){
                    // 查看当前下是否有合约
                    List<AgrContentVO> agrContentVOList = wrkWorkIpShareTree.getAgrContentVOList();
                    if(null != agrContentVOList && agrContentVOList.size()>0){
                        // 存在合约，E的数据需要删除
                        isNeedDelete.put(mapKey,true);
                        // 当前人保留map
                        remainWrkWorkIpShareMap.put(key,wrkWorkIpShareTree.getId());
                    }
                }
            }
        }

        // 找到需要删除key下的 gp
        if (isNeedDelete.size() > 0) {

            Set<String> gpAndRightTypeKeySet = initGpAndRightTypeMap.keySet();
            for (String gpAndRightTypeKey : gpAndRightTypeKeySet) {
                Boolean aBoolean = isNeedDelete.get(gpAndRightTypeKey);
                if(null != aBoolean && aBoolean){
                    BigDecimal deleteIpshare = BigDecimal.ZERO;
                    Long remainId = 0L;
                    List<WrkWorkIpShare> isNeedDeleteWrkWorkIpShareList = initGpAndRightTypeMap.get(gpAndRightTypeKey);
                    if(null == isNeedDeleteWrkWorkIpShareList || isNeedDeleteWrkWorkIpShareList.size()<1){
                        continue;
                    }
                    // 同一gp下的所有数据
                    for (WrkWorkIpShare wrkWorkIpShare : isNeedDeleteWrkWorkIpShareList) {
                        String groupIndicator = wrkWorkIpShare.getGroupIndicator();
                        String workIpRole = wrkWorkIpShare.getWorkIpRole();
                        String rightType = wrkWorkIpShare.getRightType();
                        String ipNameNo = wrkWorkIpShare.getIpNameNo();
                        BigDecimal ipShare = wrkWorkIpShare.getIpShare() == null?BigDecimal.ZERO:wrkWorkIpShare.getIpShare(); // 删除后的ipshare要回滚
                        String key = workIpRole+groupIndicator+rightType+ipNameNo;
                        Long aLong = remainWrkWorkIpShareMap.get(key);
                        if(null == aLong){
                            resultWrkWorkIpshare.removeIf(it -> key.equalsIgnoreCase(it.getWorkIpRole()+it.getGroupIndicator()+it.getRightType()+it.getIpNameNo()));
                            deleteIpshare = deleteIpshare.add(ipShare);
                        }else{
                            remainId = aLong;
                        }
                    }

                    if(remainId != 0L){
                        for (WrkWorkIpShareTree wrkWorkIpShareTree : resultWrkWorkIpshare) {
                            Long id = wrkWorkIpShareTree.getId();
                            if(null == id){
                                continue;
                            }
                            if(id.equals(remainId)){
                                BigDecimal ipShare = wrkWorkIpShareTree.getIpShare();
                                if(null == ipShare){
                                    ipShare = BigDecimal.ZERO;
                                }
                                ipShare = ipShare.add(deleteIpshare);
                                wrkWorkIpShareTree.setIpShare(ipShare);
                                wrkWorkIpShareTree.setPreIpShare(ipShare);
                            }
                        }
                    }

                }
            }


        }
        return resultWrkWorkIpshare;
    }

    // 填充规则，ipshare中无E的数据   gp自由分配  C  CA A 如果C为1 其余为null  则CA 可以是2或者3  A也一样  如果C为1 A为2 那么CA只能为3  这里要按照rightType来分组
    private List<WrkWorkIpShare> fillGp(List<WrkWorkIpShare> wrkWorkIpShareByWorkUniqueKey) {
        List<WrkWorkIpShare> fillGpIpShareList = new ArrayList<>();
        if(null != wrkWorkIpShareByWorkUniqueKey && wrkWorkIpShareByWorkUniqueKey.size()>0){
            // 按照rightType来分组
            Map<String, List<WrkWorkIpShare>> rightTypeMap = wrkWorkIpShareByWorkUniqueKey.stream().collect(Collectors.groupingBy(it -> it.getRightType()));

            Set<String> keySet = rightTypeMap.keySet();

            for (String key : keySet) {
                List<WrkWorkIpShare> workIpShareList = rightTypeMap.get(key);

                List<WrkWorkIpShare> workIpShareList1 = fillGpForRightType(workIpShareList);// 每一个rightType下的填充无gp的情况

                List<WrkWorkIpShare> workIpShareListE = fillGpForE(workIpShareList,workIpShareList1) ;

                workIpShareList1.addAll(workIpShareListE) ;

                List<WrkWorkIpShare> workIpShareList2 = changeGp(workIpShareList1);

                fillGpIpShareList.addAll(workIpShareList2);
            }
        }
        return fillGpIpShareList;
    }

    private List<WrkWorkIpShare> changeGp(List<WrkWorkIpShare> workIpShareList) {

        logger.debug("开始进行gp的转换。。。");
        List<WrkWorkIpShare> resultWrkWorkIpShareList = new ArrayList<>();
        // 根据gp来分组
        Map<String, List<WrkWorkIpShare>> gpWrkWorkIpShareMap = workIpShareList.stream().filter(a->a.getGroupIndicator() !=null).collect(Collectors.groupingBy(WrkWorkIpShare::getGroupIndicator));

        // gp为空的也计算
//        gpWrkWorkIpShareMap.put("", workIpShareList.stream().filter( a -> null == a.getGroupIndicator()).collect(Collectors.toList())) ;

        //获取最大group
        char maxGP='0';
        for (String s : gpWrkWorkIpShareMap.keySet()) {
            if (StringUtils.isBlank(s)){
                continue;
            }
            char[] chars = s.toCharArray();
            if (chars.length > 0){
                char c = chars[0];
                if (c > maxGP ){
                    maxGP =c;
                }
            }
        }


        // 获取每个gp下存在的c A CA 总数  如果》1 并且E的数量为《=1 则进行gp重新分配
        for (String key : gpWrkWorkIpShareMap.keySet()) {
            // 获取gp组下面所有的ipshare
            List<WrkWorkIpShare> wrkWorkIpShareList = gpWrkWorkIpShareMap.get(key);


            List<WrkWorkIpShare> wrkWorkIpShareAuthor = new ArrayList<>();

            List<WrkWorkIpShare> wrkWorkIpShareE = new ArrayList<>();
            List<WrkWorkIpShare> wrkWorkIpShareOther = new ArrayList<>();
            BigDecimal writerTotalIpShare = BigDecimal.ZERO;
            for (WrkWorkIpShare wrkWorkIpShare : wrkWorkIpShareList) {
                if (Constants.WORK_IP_ROLE_ORIGNIAL.contains(wrkWorkIpShare.getWorkIpRole())){
                    wrkWorkIpShareAuthor.add(wrkWorkIpShare);
                    if (wrkWorkIpShare.getIpShare() !=null){
                        writerTotalIpShare = writerTotalIpShare.add( wrkWorkIpShare.getIpShare());

                    }

                }else if (StringUtils.equalsAnyIgnoreCase(wrkWorkIpShare.getWorkIpRole(),"E")){
                    wrkWorkIpShareE.add(wrkWorkIpShare);
                }else if(Constants.WORK_IP_ROLE_UPDATE.contains(wrkWorkIpShare.getWorkIpRole())){
                    wrkWorkIpShareOther.add(wrkWorkIpShare);
                }
            }

            if (writerTotalIpShare.compareTo(BigDecimal.ZERO) ==0 || wrkWorkIpShareAuthor.size()<=1){
                resultWrkWorkIpShareList.addAll(wrkWorkIpShareList);
                continue;
            }else {
                resultWrkWorkIpShareList.addAll(wrkWorkIpShareOther);
            }


            for (int i = 0; i < wrkWorkIpShareAuthor.size(); i++) {

                WrkWorkIpShare wrkWorkIpShare = wrkWorkIpShareAuthor.get(i);
                if (i>0){
                    //ascii 码 数字9对应的是57，字符A对应的是65  a对应的是97
                    maxGP++;
                    if (maxGP == 58){
                        maxGP = 65;
                    }
                    wrkWorkIpShare.setGroupIndicator(maxGP+"");
                }
                resultWrkWorkIpShareList.add(wrkWorkIpShare);


                BigDecimal ipShare = wrkWorkIpShare.getIpShare();
                if (ipShare==null){
                    ipShare=BigDecimal.ZERO;
                }
                BigDecimal keyRate = ipShare.divide(writerTotalIpShare,6, RoundingMode.HALF_UP);

                //将E copy一份到每一个WriteGp下
                for (WrkWorkIpShare eWorkIpShare : wrkWorkIpShareE) {

                    BigDecimal ipShareE = BigDecimal.ZERO;
                    WrkWorkIpShare wrkWorkIpShare1 = new WrkWorkIpShare();
                    BeanUtils.copyProperties(eWorkIpShare,wrkWorkIpShare1);
                    ipShareE = wrkWorkIpShare1.getIpShare();
                    if (ipShareE ==null){
                        ipShareE = BigDecimal.ZERO;
                    }
                    wrkWorkIpShare1.setGroupIndicator(wrkWorkIpShare.getGroupIndicator());

                    ipShareE = ipShareE.multiply(keyRate).setScale(6,RoundingMode.HALF_UP);
                    //logger.info("计算后的E的ipshare为："+ipShareE);
                    wrkWorkIpShare1.setIpShare(ipShareE);
                    wrkWorkIpShare1.setId(null);
                    resultWrkWorkIpShareList.add(wrkWorkIpShare1);
                }
            }
        }




       /* logger.info("最终结果>>>>>>>>>>>>>>"+"\r\n");
        resultWrkWorkIpShareList.forEach(resultWrkWorkIpShare ->{
            logger.info("当前gp为："+resultWrkWorkIpShare.getGroupIndicator()+";当前的ipshare为："+resultWrkWorkIpShare.getIpShare()+";\r\n"
                    +"当前的workIpRole ： "+resultWrkWorkIpShare.getWorkIpRole()+"；当前的ipNameNo : "+resultWrkWorkIpShare.getIpNameNo()+"当前作品workWorkUnique : "+resultWrkWorkIpShare.getWorkUniqueKey());
        });*/

        return resultWrkWorkIpShareList;
    }

    // TODO 逻辑有点问题 需要调整下
    private List<WrkWorkIpShare> fillGpForRightType(List<WrkWorkIpShare> workIpShareList) {
        List<WrkWorkIpShare> resultList = new ArrayList<>();
//        List<WrkWorkIpShare> containsEList = workIpShareList.stream().filter(it -> "E".equalsIgnoreCase(it.getWorkIpRole())).collect(Collectors.toList());
//        if(!CollectionUtils.isEmpty(containsEList)){
//            return workIpShareList;
//        }

        // 获取gp为空的数据
        List<WrkWorkIpShare> gpIsBlankList = workIpShareList.stream().filter(it -> StringUtils.isBlank(it.getGroupIndicator()) && !StringUtils.containsAny(it.getWorkIpRole(),"E")).collect(Collectors.toList());
        // 获取gp不为空的数据
        List<WrkWorkIpShare> gpIsNotBlankList = workIpShareList.stream().filter(it -> StringUtils.isNotBlank(it.getGroupIndicator())).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(gpIsBlankList)){
            return workIpShareList;
        }

        char maxGP='0';
        if(!CollectionUtils.isEmpty(gpIsNotBlankList)){

            for (WrkWorkIpShare wrkWorkIpShare : gpIsNotBlankList) {
                String groupIndicator = wrkWorkIpShare.getGroupIndicator();
                if (StringUtils.isBlank(groupIndicator)){
                    continue;
                }
                char[] chars = groupIndicator.toCharArray();
                if (chars.length > 0){
                    char c = chars[0];
                    if (c > maxGP ){
                        maxGP =c;
                    }
                }
            }
            resultList.addAll(gpIsNotBlankList);
        }

        // 给gp重新赋值 ，根据获取到最大的gp累加，1-9，A-Z形式
        for (WrkWorkIpShare wrkWorkIpShare : gpIsBlankList) {

            //ascii 码 数字9对应的是57，字符A对应的是65  a对应的是97
            maxGP++;
            if (maxGP == 58){
                maxGP = 65;
            } else if(maxGP == 91){ // 26/1265900734 这首歌不够用
                maxGP = 97;
            }
            wrkWorkIpShare.setGroupIndicator(maxGP+"");
        }
        resultList.addAll(gpIsBlankList);

        return resultList;
    }

    private List<WrkWorkIpShare> fillGpForE(List<WrkWorkIpShare> workIpShareList,List<WrkWorkIpShare> caWorkIpShareList){

        List<WrkWorkIpShare> gpIsBlankListE = workIpShareList.stream().filter(it -> StringUtils.isBlank(it.getGroupIndicator())
                && StringUtils.containsAny(it.getWorkIpRole(),"E")
                && (it.getOipLinkId() == null || it.getOipLinkId() == 0)).collect(Collectors.toList());

        List<WrkWorkIpShare> results = new ArrayList<>();
        if(CollectionUtils.isEmpty(gpIsBlankListE)){
            return results;
        }

        BigDecimal caTotal = caWorkIpShareList.stream().filter(it -> it.getIpShare() != null).map(it -> it.getIpShare()).reduce( BigDecimal::add).get();
        if(caTotal.compareTo(BigDecimal.ZERO) == 0){
            caTotal = BigDecimal.valueOf(caWorkIpShareList.size()) ;
        }
        for(WrkWorkIpShare caIpshare : caWorkIpShareList){
            BigDecimal rate = caIpshare.getIpShare().divide(caTotal,6,RoundingMode.HALF_UP);
            BigDecimal ipshareToE = BigDecimal.ZERO;
            for(WrkWorkIpShare eIpshare : gpIsBlankListE){
                if(eIpshare.getIpShare().compareTo(BigDecimal.ZERO ) < 1){
                    continue;
                }
                BigDecimal ipshare = eIpshare.getIpShare().multiply(rate);
                ipshareToE = ipshareToE.add(ipshare) ;
                WrkWorkIpShare copyIpshre = new WrkWorkIpShare();
                BeanUtils.copyProperties(eIpshare,copyIpshre);
                copyIpshre.setGroupIndicator(caIpshare.getGroupIndicator());
                copyIpshre.setIpShare(ipshare);
                copyIpshre.setOrignalWrkIpShare(BigDecimal.ZERO);
                copyIpshre.setOrgWriterShare(BigDecimal.ZERO);
                results.add(copyIpshre);
            }
            caIpshare.setOrignalWrkIpShare(caIpshare.getIpShare().add(ipshareToE));
            caIpshare.setOrgWriterShare(caIpshare.getIpShare().add(ipshareToE));
        }
        return results;
    }

    private List<WrkWorkIpShareTree> removePersonWrite(List<WrkWorkIpShareTree> wrkWorkIpShareTreeList, List<WrkWorkIpShareTree> personWrite) {

        List<WrkWorkIpShareTree> wrkWorkIpShareTreeList2 = new ArrayList<>();
        wrkWorkIpShareTreeList2.addAll(wrkWorkIpShareTreeList);
        // 如果是重复的数据，那么其isrepeat=true,找到同样的数据且oipLinkId=0的删除即可
        if(null != personWrite && personWrite.size()>0){
            Iterator<WrkWorkIpShareTree> iterator = wrkWorkIpShareTreeList.iterator();
            while(iterator.hasNext()){
                WrkWorkIpShareTree wrkWorkIpShareTree = iterator.next();
                List<WrkWorkIpShareTree> workIpShareList = wrkWorkIpShareTree.getWorkIpShareList();
                if(null != workIpShareList && workIpShareList.size()>0){
                    List<WrkWorkIpShareTree> repeatList = workIpShareList.stream().filter(it -> it.isRepeat()).collect(Collectors.toList());
                    for (WrkWorkIpShareTree bodyWrkWorkIpShareTree : repeatList) {
                        String groupIndicator = bodyWrkWorkIpShareTree.getGroupIndicator();
                        String ipNameNo = bodyWrkWorkIpShareTree.getIpNameNo();
                        String ipBaseNo = bodyWrkWorkIpShareTree.getIpBaseNo();
                        String rightType = bodyWrkWorkIpShareTree.getRightType();
                        Long sipLinkId = bodyWrkWorkIpShareTree.getSipLinkId();
                        String key = groupIndicator+ipNameNo+ipBaseNo+rightType+sipLinkId;
                        Map<String, List<WrkWorkIpShareTree>> keyMap = wrkWorkIpShareTreeList.stream().collect(Collectors.groupingBy(it -> it.getGroupIndicator() + it.getIpNameNo() + it.getIpBaseNo() + it.getRightType() + it.getSipLinkId()));
                        if(keyMap.containsKey(key)){
                            List<WrkWorkIpShareTree> wrkWorkIpShareTreeList1 = keyMap.get(key);
                            for (WrkWorkIpShareTree workIpShareTree : wrkWorkIpShareTreeList1) {
                                Long oipLinkId = 0L;
                                if(oipLinkId.equals(workIpShareTree.getOipLinkId())){
                                    wrkWorkIpShareTreeList2.remove(workIpShareTree);
                                }
                            }
                        }
                    }
                }
            }
        }
        return wrkWorkIpShareTreeList2;
    }

    private List<WrkWorkIpShare> changeTreeToIpShare(List<WrkWorkIpShareTree> sortByWorkIpRole) {
        List<WrkWorkIpShare> resultList = new ArrayList<>();
        if(null != sortByWorkIpRole && sortByWorkIpRole.size()>0){
            for (WrkWorkIpShareTree wrkWorkIpShareTree : sortByWorkIpRole) {
                WrkWorkIpShare wrkWorkIpShare = changeToWrkWorkIpShare(wrkWorkIpShareTree);
                /*if(StringUtils.isNotEmpty(wrkWorkIpShare.getAgrNo())){
                    wrkWorkIpShare.setOrgWriterShare(BigDecimal.ZERO);
                    wrkWorkIpShare.setOrignalWrkIpShare(BigDecimal.ZERO);
                }*/
                resultList.add(wrkWorkIpShare);
            }
        }
        return resultList;
    }

    private void calcIpShare(WrkWorkIpShareTree wrkWorkIpShareTree) {
        if(wrkWorkIpShareTree.isHasBody()){
            // 子节点下的人
            List<WrkWorkIpShareTree> workIpShareList = wrkWorkIpShareTree.getWorkIpShareList();
            for (WrkWorkIpShareTree workIpShareTree : workIpShareList) {
                String rightType = workIpShareTree.getRightType();
                Map<String, BigDecimal> assigneeRightTypeShare = workIpShareTree.getAssigneeRightTypeShare();
                if(null == assigneeRightTypeShare || assigneeRightTypeShare.size()<1){
                    continue;
                }
                BigDecimal bigDecimal = assigneeRightTypeShare.get(workIpShareTree.getAgrNo()+rightType);
                if(null == bigDecimal){
                    bigDecimal = BigDecimal.ZERO;
                }
                BigDecimal ipShare = wrkWorkIpShareTree.getPreIpShare();
                BigDecimal ipShare1 = wrkWorkIpShareTree.getIpShare();
                if(null == ipShare1){
                    continue;
                }
                if(ipShare == null){
                    ipShare = BigDecimal.ZERO;
                }

                AgrContentVO assigneeAgrContentVO  = workIpShareTree.getAgrContentVO() == null ? new AgrContentVO() : workIpShareTree.getAgrContentVO();

                Double monthValue = assigneeAgrContentVO.getMonth();
                if(null == monthValue){
                    monthValue = Double.valueOf("0");
                }
                BigDecimal month = new BigDecimal(monthValue);
//                try {
//                    month = setMonth(wrkWorkIpShareTree, workIpShareTree);
//                } catch (ParseException e) {
//                    logger.info("日期轉換錯誤~");
//                    e.printStackTrace();
//                }


                BigDecimal assigneeIpShare = bigDecimal.multiply(ipShare).multiply(month).divide(new BigDecimal(100), 6, RoundingMode.HALF_UP);
                workIpShareTree.setIpShare(assigneeIpShare);
                workIpShareTree.setPreIpShare(assigneeIpShare);
                BigDecimal subtract = ipShare1.subtract(assigneeIpShare);
                if(subtract.compareTo(BigDecimal.ZERO)<0){
                    subtract = BigDecimal.ZERO;
                }
                wrkWorkIpShareTree.setIpShare(subtract);
                calcIpShare(workIpShareTree);
                AgrContentVO agrContentVO = wrkWorkIpShareTree.getAgrContentVO() == null ? new AgrContentVO() : wrkWorkIpShareTree.getAgrContentVO();

                if (logger.isDebugEnabled()) {
                    logger.debug("当前人ipNameNo : " + workIpShareTree.getIpNameNo() + "; 当前的比例为：" + workIpShareTree.getIpShare() + ";当前月份month：" + month + ";当前Assignor的month" + agrContentVO.getMonth() + ";当前Assignee的month:" + assigneeAgrContentVO.getMonth());
                }
            }
        }
    }

    private void calcIpShareADP(WrkWorkIpShareTree wrkWorkIpShareTree) {
        if(wrkWorkIpShareTree.isHasBody()){
            // 子节点下的人
            List<WrkWorkIpShareTree> workIpShareList = wrkWorkIpShareTree.getWorkIpShareList();
            BigDecimal shareToALl,shareToOne = BigDecimal.ZERO;
            if(Constants.WORK_IP_ROLE_ORIGNIAL.contains(wrkWorkIpShareTree.getWorkIpRole())){
                BigDecimal toMipshare = workIpShareList.stream().filter(it -> it.getIpsharem() != null && BigDecimal.ZERO.compareTo(it.getIpsharem()) == -1).map(it-> it.getIpsharem()).reduce(BigDecimal.ZERO, BigDecimal::add) ;
                BigDecimal mipshare = wrkWorkIpShareTree.getIpsharem() == null ? BigDecimal.ZERO : wrkWorkIpShareTree.getIpsharem();
                BigDecimal ipshare = toMipshare.add(mipshare);
                wrkWorkIpShareTree.setIpsharem(ipshare);
                wrkWorkIpShareTree.setIpShare(ipshare);

                /*List<WrkWorkIpShareTree> assignorList = wrkWorkIpShareTree.getAssignorList();
                int paNum = 0 ;
                Set<String> agrNoset = new HashSet<>();
                List<AgrContentVO> agrContentVOList = wrkWorkIpShareTree.getAgrContentVOList();
                if(!CollectionUtils.isEmpty(agrContentVOList)){
                    agrNoset = agrContentVOList.stream().map(AgrContentVO :: getAgrNo).collect(Collectors.toSet());
                }
                for(WrkWorkIpShareTree workIpShareTree : assignorList){
                    if(Constants.WORK_IP_ROLE_ORIGNIAL.contains(workIpShareTree.getWorkIpRole())){
                        Map<String, BigDecimal> assignorRightTypeShare = workIpShareTree.getAssignorRightTypeShare();
                        BigDecimal myshare = assignorRightTypeShare.values().stream().collect(Collectors.toList()).get(0);
                        if(myshare.compareTo(BigDecimal.ZERO) == 1){
                            paNum ++ ;
                        }
                    } else if(StringUtils.equals("PA",workIpShareTree.getWorkIpRole())){
                        if(!agrNoset.contains(workIpShareTree.getAgrNo())){
                            continue;
                        }
                        Map<String, BigDecimal> assigneeRightTypeShare = workIpShareTree.getAssigneeRightTypeShare();
                        if(null == assigneeRightTypeShare || assigneeRightTypeShare.size()<1){
                            continue;
                        }
                        BigDecimal myshare = assigneeRightTypeShare.get(workIpShareTree.getAgrNo()+workIpShareTree.getRightType());
                        if(myshare.compareTo(BigDecimal.ZERO) == 1){
                            paNum ++ ;
                        }
                    }
                }
*/

                int paNum = 0 ;
                Map<String, BigDecimal> assignorRightTypeShare = wrkWorkIpShareTree.getAssignorRightTypeShare();
                BigDecimal myshare = assignorRightTypeShare.values().stream().collect(Collectors.toList()).get(0);
                if(myshare.compareTo(BigDecimal.ZERO) == 0){
                    for(WrkWorkIpShareTree workIpShareTree : workIpShareList){
                        if(StringUtils.equals("PA",workIpShareTree.getWorkIpRole())){
                            Map<String, BigDecimal> assigneeRightTypeShare = workIpShareTree.getAssigneeRightTypeShare();
                            if(null == assigneeRightTypeShare || assigneeRightTypeShare.size()<1){
                                continue;
                            }
                            myshare = assigneeRightTypeShare.get(workIpShareTree.getAgrNo()+workIpShareTree.getRightType());
                            if(myshare.compareTo(BigDecimal.ZERO) == 1){
                                paNum ++ ;
                            }
                        }
                    }
                }

                if(paNum > 0){
                    shareToALl = wrkWorkIpShareTree.getPreIpShare().subtract(wrkWorkIpShareTree.getIpShare());
                    shareToOne = shareToALl ;

                    if(paNum > 1){
                        shareToOne = shareToALl.divide(new BigDecimal(paNum),6,RoundingMode.HALF_UP );
                    }
                }

            } else if(wrkWorkIpShareTree.getWorkIpRole().equals("E") && StringUtils.isEmpty( wrkWorkIpShareTree.getAgrNo())){
                if(wrkWorkIpShareTree.getIpsharem() != null){
                    wrkWorkIpShareTree.setIpShare(wrkWorkIpShareTree.getIpsharem());
                    wrkWorkIpShareTree.setPreIpShare(wrkWorkIpShareTree.getIpsharem());
                }
            }


            for (WrkWorkIpShareTree workIpShareTree : workIpShareList) {
                String rightType = workIpShareTree.getRightType();
                Map<String, BigDecimal> assigneeRightTypeShare = workIpShareTree.getAssigneeRightTypeShare();
                if(null == assigneeRightTypeShare || assigneeRightTypeShare.size()<1){
                    continue;
                }
                BigDecimal bigDecimal = assigneeRightTypeShare.get(workIpShareTree.getAgrNo()+rightType);
                if(null == bigDecimal){
                    bigDecimal = BigDecimal.ZERO;
                }
                BigDecimal preIpShare = wrkWorkIpShareTree.getPreIpShare();
                BigDecimal ipShare = wrkWorkIpShareTree.getIpShare();
                if(null == ipShare){
                    continue;
                }
                if(preIpShare == null){
                    preIpShare = BigDecimal.ZERO;
                }


                AgrContentVO assigneeAgrContentVO  = workIpShareTree.getAgrContentVO() == null ? new AgrContentVO() : workIpShareTree.getAgrContentVO();

                Double monthValue = assigneeAgrContentVO.getMonth();
                if(null == monthValue){
                    monthValue = Double.valueOf("0");
                }
                BigDecimal month = new BigDecimal(monthValue);
//                try {
//                    month = setMonth(wrkWorkIpShareTree, workIpShareTree);
//                } catch (ParseException e) {
//                    logger.info("日期轉換錯誤~");
//                    e.printStackTrace();
//                }


                BigDecimal assigneeIpShare = bigDecimal.multiply(preIpShare).multiply(month).divide(new BigDecimal(100), 6, RoundingMode.HALF_UP);
                if(workIpShareTree.getWorkIpRole().equals("PA")){
                    assigneeIpShare = assigneeIpShare.subtract(shareToOne);
                    if(assigneeIpShare.compareTo(BigDecimal.ZERO) == -1){
                        assigneeIpShare = BigDecimal.ZERO;
                        ipShare = ipShare.subtract(shareToOne);
                    }
                    workIpShareTree.setIpShare(assigneeIpShare);
                } else {
                    workIpShareTree.setIpShare(assigneeIpShare);
                }
                workIpShareTree.setPreIpShare(assigneeIpShare);
                BigDecimal subtract = ipShare.subtract(assigneeIpShare);
                if(subtract.compareTo(BigDecimal.ZERO)<0){
                    subtract = BigDecimal.ZERO;
                }
                wrkWorkIpShareTree.setIpShare(subtract);
                calcIpShareADP(workIpShareTree);
                AgrContentVO agrContentVO = wrkWorkIpShareTree.getAgrContentVO() == null ? new AgrContentVO() : wrkWorkIpShareTree.getAgrContentVO();

                if (logger.isDebugEnabled()) {
                    logger.debug("当前人ipNameNo : " + workIpShareTree.getIpNameNo() + "; 当前的比例为：" + workIpShareTree.getIpShare() + ";当前月份month：" + month + ";当前Assignor的month" + agrContentVO.getMonth() + ";当前Assignee的month:" + assigneeAgrContentVO.getMonth());
                }
            }
        } else {
            if(wrkWorkIpShareTree.getIpsharem() != null){
                wrkWorkIpShareTree.setIpShare(wrkWorkIpShareTree.getIpsharem());
            }
        }
    }

    // TODO  这里需要调整下
    private BigDecimal setMonth(WrkWorkIpShareTree wrkWorkIpShareTree, WrkWorkIpShareTree workIpShareTree) throws ParseException {
        BigDecimal month = BigDecimal.ZERO;
        AgrContentVO agrContentVOAssignor = wrkWorkIpShareTree.getAgrContentVO();
        AgrContentVO agrContentVOAssignee = workIpShareTree.getAgrContentVO();
        if(agrContentVOAssignor == null){
            month = agrContentVOAssignee.getMonth() == null?BigDecimal.ZERO:new BigDecimal(agrContentVOAssignee.getMonth()).setScale(6,RoundingMode.HALF_UP);
        }else{
            // 判断2个合约时间是否一致  一致的话 month充值为1  不一致 开始时间取 开始时间取最大，结束时间取最小   交集   month = 交集之间的月份总数/assignor的年度总月数
            Date assignorAgrSdate = agrContentVOAssignor.getAgrSdate();
            Date assignorAgrEdate = agrContentVOAssignor.getAgrEdate();

            Date assigneeAgrSdate = agrContentVOAssignee.getAgrSdate();
            Date assigneeAgrEdate = agrContentVOAssignee.getAgrEdate();

            Date startDate = null;
            Date endDate = null;

            if(assignorAgrSdate.compareTo(assigneeAgrSdate) >= 0){
                startDate = assignorAgrSdate;
            }else{
                startDate = assigneeAgrSdate;
            }
            if(assignorAgrEdate.compareTo(assigneeAgrEdate) <= 0){
                endDate = assignorAgrEdate;
            }else{
                endDate = assigneeAgrEdate;
            }
            String calcEndDateStr = wrkWorkIpShareTree.getYear()+"-12-01";
            Date calcEndDate = new SimpleDateFormat("yyyy-MM-dd").parse(calcEndDateStr);
            if(endDate.compareTo(calcEndDate)>=0){
                endDate = calcEndDate;
                assignorAgrEdate = calcEndDate;
            }
            String calcStartDateStr = wrkWorkIpShareTree.getYear()+"-01-01";
            Date calcStartDate = new SimpleDateFormat("yyyy-MM-dd").parse(calcStartDateStr);
            if(startDate.compareTo(calcStartDate)<=0){
                startDate = calcStartDate;
                assignorAgrSdate = calcStartDate;
            }

            int monthDiff = DateUtils.getMonthDiff(startDate, endDate);
            if(monthDiff<0){
                return BigDecimal.ZERO;
            }
            int assignorDiff = DateUtils.getMonthDiff(assignorAgrSdate, assignorAgrEdate);
            month = new BigDecimal(monthDiff).divide(new BigDecimal(assignorDiff),6, RoundingMode.HALF_UP);
        }
        return month;
    }

    /**
     * 判断公共财的人是C A CA  如果是CA那么 C为50% A为50%  如果是A 则包含A的有多少人平均分
     * 例如：CA 50% A 30% C 20%   CA为公共财
     *  不是公共财的有  C  A   C的个数为1 A的个数为1
     *  CA 拆分C的占比为50%*50% A的占比为50%*50%
     * 计算C = C的占比/C的个数 + C的比例 = 25%/1 + 20% = 45%
     * 计算A = A的占比/A的个数 + A的比例 = 25%/1 + 30% = 55%
     * @param sortByWorkIpRole
     * @param year
     */
    private void dealCommonProperty(List<WrkWorkIpShareTree> sortByWorkIpRole, String year) {
        if (null != sortByWorkIpRole && sortByWorkIpRole.size() > 0) {
            List<WrkWorkIpShareTree> assetShareList = new ArrayList<>();
            List<WrkWorkIpShareTree> notAssetShareList = new ArrayList<>();
            List<WrkWorkIpShareTree> notAssetUpdateShareList = new ArrayList<>();
            int CCount = 0;
            int ACount = 0;
            int totalOrignialWorkIpRole = 0;
            for (WrkWorkIpShareTree wrkWorkIpShare : sortByWorkIpRole) {
                String ipNameNo = wrkWorkIpShare.getIpNameNo();
                String workIpRole = wrkWorkIpShare.getWorkIpRole();
                // 是否是公共财产   要依据合约计算的年份 TODO   由原先查库 更新为缓存内存
//                boolean sharedAssets = wrkWorkIpShareService.isSharedAssets(ipNameNo, year);

//                boolean sharedAssets = checkIsAssets(wrkWorkIpShare);

                if(StringUtils.equals(wrkWorkIpShare.getIpSocietyCode(),"000")){
                    // deal 公共财产  规则： C A  CA
                    assetShareList.add(wrkWorkIpShare);

                }else{
                    if(Constants.WORK_IP_ROLE_ORIGNIAL.contains(workIpRole)){
                        notAssetShareList.add(wrkWorkIpShare);
                        String notAssetWorkIpRole = wrkWorkIpShare.getWorkIpRole();
                        // 统计有多少个C  有多少个A
                        if("CA".equalsIgnoreCase(notAssetWorkIpRole)){
                            CCount += 1;
                            ACount += 1;
                        }
                        if("C".equalsIgnoreCase(notAssetWorkIpRole)){
                            CCount += 1;
                        }
                        if("A".equalsIgnoreCase(notAssetWorkIpRole)){
                            ACount += 1;
                        }
                        totalOrignialWorkIpRole += 1;
                    }
                    if(Constants.WORK_IP_ROLE_UPDATE.contains(workIpRole)){
                        notAssetUpdateShareList.add(wrkWorkIpShare);
                    }
                }
            }

            if(notAssetShareList.size()<1 && notAssetUpdateShareList.size()>0){
                notAssetShareList.addAll(notAssetUpdateShareList);
            }

            Map<String, List<WrkWorkIpShareTree>> noAssetIpShareMap = notAssetShareList.stream().collect(Collectors.groupingBy(it -> it.getRightType()));
            // 只处理有公共财产的   原則: 版權比例都不會因為其中一個IP為000所變動，除非整首CA都已經是000，版權比例才會歸0
            if(assetShareList.size()>0){
                Map<String, BigDecimal> noAssetMap = notAssetShareList.stream().filter(it ->null != it.getIpShare()).collect(Collectors.groupingBy(it -> it.getRightType(), CollectorsUtil.summingBigDecimal(WrkWorkIpShareTree::getIpShare)));
                BigDecimal calcIpShare = BigDecimal.ZERO;
                for (WrkWorkIpShareTree wrkWorkIpShare : assetShareList) {
                    BigDecimal CIpShare = BigDecimal.ZERO;
                    BigDecimal AIpShare = BigDecimal.ZERO;
                    wrkWorkIpShare.setIpSocietyCode("000");
                    String workIpRole = wrkWorkIpShare.getWorkIpRole();
                    // 如果是公共财产的部分 则取 OriginalIpShare
//                    BigDecimal ipShare = wrkWorkIpShare.getOrgWriterShare()== null?BigDecimal.ZERO:wrkWorkIpShare.getOrgWriterShare();
                    BigDecimal ipShare = wrkWorkIpShare.getIpShare() == null?BigDecimal.ZERO:wrkWorkIpShare.getIpShare();
//                    String ipNameNo = wrkWorkIpShare.getIpNameNo();
                    String groupIndicator = wrkWorkIpShare.getGroupIndicator();
                    String rightType = wrkWorkIpShare.getRightType();
//                    String key = workIpRole+ipNameNo;
                    List<WrkWorkIpShareTree> noAssetWrkWorkIpShareTreeList = noAssetIpShareMap.get(rightType);

                    // 回归计算规则
                    // 1、如果改词改曲、SA为公共财时，那么这个作品的ip share比例全为0不分。
                    if(Constants.WORK_IP_ROLE_UPDATE.contains(workIpRole)){
                        for (WrkWorkIpShareTree workIpShare : sortByWorkIpRole) {
                            workIpShare.setIpShare(BigDecimal.ZERO);
                        }
                    }

                    // 2、公共财会不会出现给E的情况，这样E的比例要回收回来   CA 50%  A(0%) C -> E(0%)->SE(50%)  A公共财产  整个Group 下的ipshare累加起来，给到公共财产的人， 再将公共财产的人比例给到剩下有权利的人
                    if(StringUtils.isBlank(groupIndicator)){
                        calcIpShare = ipShare;
                    }else{

                        // 表示没有同一级别的ip了，如果有则不需要回归下面所有的比例  仅仅只是分摊当前人的比例
                        if(totalOrignialWorkIpRole == 0){
                            // 该组下所有人的ipshare加在一起
                            calcIpShare = sortByWorkIpRole.stream()
                                    .filter(it -> it.getGroupIndicator() !=null && groupIndicator.contains(it.getGroupIndicator()) && null != it.getIpShare() && rightType.equalsIgnoreCase(it.getRightType()))
                                    .map(WrkWorkIpShareTree::getIpShare).reduce(BigDecimal.ZERO, BigDecimal::add);

                            // 需要将当前group下的所有人ipshare设为0
                            for (WrkWorkIpShareTree workIpShare : sortByWorkIpRole) {

                                String groupIndicator1 = workIpShare.getGroupIndicator();
                                if (groupIndicator1 ==null){
                                    continue;
                                }
                                if(groupIndicator.contains(groupIndicator1) && rightType.equalsIgnoreCase(workIpShare.getRightType())){
                                    workIpShare.setIpShare(BigDecimal.ZERO);
                                }
                            }
                        }else{
                            calcIpShare = ipShare;
                        }
                    }

                    if(calcIpShare == null){
                        calcIpShare = BigDecimal.ZERO;
                    }
                    if("CA".equalsIgnoreCase(workIpRole)){
                        CIpShare = calcIpShare.divide(new BigDecimal("2"),6,RoundingMode.HALF_UP);
                        AIpShare = calcIpShare.divide(new BigDecimal("2"),6,RoundingMode.HALF_UP);
                    }
                    if("C".equalsIgnoreCase(workIpRole)){
                        CIpShare = calcIpShare;
                    }
                    if("A".equalsIgnoreCase(workIpRole)){
                        AIpShare = calcIpShare;
                    }
                    if(CollectionUtils.isEmpty(noAssetWrkWorkIpShareTreeList)){
                        continue;
                    }

                    // FIXME 判断C是否是0个 A是否是0个  如果C和A同时都是0个的时候 那么比例均为0； 如果C是0个，那么C的比例全部给到A； 反之一样
                    if(CCount == 0 && ACount == 0 && (CIpShare.compareTo(BigDecimal.ZERO) > 0 || AIpShare.compareTo(BigDecimal.ZERO) > 0)) {
                        for (WrkWorkIpShareTree wrkWorkIpShareTree : noAssetWrkWorkIpShareTreeList) {
                            wrkWorkIpShareTree.setIpShare(BigDecimal.ZERO);
                        }
                        continue;
                    }

                    if(CCount == 0){
                        AIpShare = AIpShare.add(CIpShare);
                    }

                    if(ACount == 0){
                        CIpShare = CIpShare.add(AIpShare);
                    }

                    for (WrkWorkIpShareTree workIpShare : noAssetWrkWorkIpShareTreeList) {
                        String noAssetWrkIpShare = workIpShare.getWorkIpRole();
                        BigDecimal noAssetIpShare = workIpShare.getIpShare();
                        if (noAssetIpShare ==null){
                            continue;
                        }
                        BigDecimal finalIpShare = BigDecimal.ZERO;
                        if("CA".equalsIgnoreCase(noAssetWrkIpShare)){
                            if(CCount != 0){
                                finalIpShare = CIpShare.divide(new BigDecimal(CCount),6,RoundingMode.HALF_UP);
                            }
                            if(ACount != 0){
                                finalIpShare = finalIpShare.add(AIpShare.divide(new BigDecimal(ACount),6,RoundingMode.HALF_UP));
                            }
                            finalIpShare = finalIpShare.add(noAssetIpShare);
                            workIpShare.setIpShare(finalIpShare);
                            continue;
                        }
                        if("C".equalsIgnoreCase(noAssetWrkIpShare)){
                            if(CCount != 0){
                                finalIpShare = CIpShare.divide(new BigDecimal(CCount),6,RoundingMode.HALF_UP);
                            }
                            finalIpShare = finalIpShare.add(noAssetIpShare);
                            workIpShare.setIpShare(finalIpShare);
                            continue;
                        }
                        if("A".equalsIgnoreCase(noAssetWrkIpShare)){
                            if(ACount != 0){
                                finalIpShare = finalIpShare.add(AIpShare.divide(new BigDecimal(ACount),6,RoundingMode.HALF_UP));
                            }
                            finalIpShare = finalIpShare.add(noAssetIpShare);
                            workIpShare.setIpShare(finalIpShare);
                        }
                    }
                }


//                    // 计算  C  A  CA   A为公共财   A回归到CA   C为公共财 C回到CA CA为公共财  均摊到C/A   C A  不成立 废弃规则 FIXME
//                    int size = noAssetWrkWorkIpShareTreeList.size();
//                    if(size == 1){
//                        WrkWorkIpShareTree wrkWorkIpShare1 = noAssetWrkWorkIpShareTreeList.get(0);
//                        BigDecimal ipShare1 = wrkWorkIpShare1.getIpShare() == null?BigDecimal.ZERO:wrkWorkIpShare1.getIpShare();
//                        ipShare1 = ipShare1.add(calcIpShare);
//                        noAssetWrkWorkIpShareTreeList.get(0).setIpShare(ipShare1);
//                    }else{
//                        Map<String, List<WrkWorkIpShareTree>> workIpRoleMap = noAssetWrkWorkIpShareTreeList.stream().collect(Collectors.groupingBy(WrkWorkIpShareTree::getWorkIpRole));
//                        // 此处需要知道有几个C A  CA  同时每个C A CA 是谁   在根据占比去计算  TODO
//                        for (WrkWorkIpShareTree workIpShare : noAssetWrkWorkIpShareTreeList) {
//                            String workIpRole1 = workIpShare.getWorkIpRole();
//                            String ipNameNo1 = workIpShare.getIpNameNo();
//                            BigDecimal ipShare1 = workIpShare.getIpShare()==null?BigDecimal.ZERO:workIpShare.getIpShare();
//                            String rightType1 = workIpShare.getRightType();
//                            if(!rightType.equalsIgnoreCase(rightType1)){
//                                continue;
//                            }
//                            String key1 = workIpRole1+ipNameNo1;
//                            List<WrkWorkIpShareTree> wrkWorkIpShareTreeList = workIpRoleMap.get(workIpRole1);
//                            // 如果存在包含  则直接给到包含的人 如果这个总的PER下面有三个CA  其中一个成了公共财产，另外两个需要按照相应的比例去分摊
//                            if(workIpRole1.contains(workIpRole) && Constants.WORK_IP_ROLE_ORIGNIAL.contains(workIpRole1) && wrkWorkIpShareTreeList.size() == 1){
//                                workIpShare.setIpShare(ipShare1.add(calcIpShare));
//                                calcIpShare = BigDecimal.ZERO;
//                            }else{
//                                BigDecimal remainValue = noAssetMap.get(rightType1);
//                                if(null == remainValue || BigDecimal.ZERO.compareTo(remainValue) == 0){
//                                    workIpShare.setIpShare(BigDecimal.ZERO);
//                                }else{
//                                    BigDecimal add = calcIpShare.multiply(ipShare1.divide(remainValue, 6, RoundingMode.HALF_UP)).add(ipShare1);
//                                    workIpShare.setIpShare(add);
//                                }
//                            }
//                        }
//                    }


                // 更新ipShare
                for (WrkWorkIpShareTree wrkWorkIpShare : sortByWorkIpRole) {
                    Long id = wrkWorkIpShare.getId();
                    for (WrkWorkIpShareTree ipShare : assetShareList) {
                        Long id1 = ipShare.getId();
                        if(id1.equals(id)){
                            wrkWorkIpShare.setIpShare(BigDecimal.ZERO);
                            wrkWorkIpShare.setIpSocietyCode(ipShare.getIpSocietyCode());
                        }
                    }

                    String rightType = wrkWorkIpShare.getRightType();
                    List<WrkWorkIpShareTree> wrkWorkIpShareTreeList = noAssetIpShareMap.get(rightType);
                    if(null == wrkWorkIpShareTreeList || wrkWorkIpShareTreeList.size()<1){
                        continue;
                    }
                    for (WrkWorkIpShareTree ipShare : wrkWorkIpShareTreeList) {
                        Long id1 = ipShare.getId();
                        if(id1.equals(id)){
                            wrkWorkIpShare.setIpShare(ipShare.getIpShare());
                        }
                    }

                }
            }

//                // 更新ipShare
//                for (WrkWorkIpShareTree wrkWorkIpShare : sortByWorkIpRole) {
//                    Long id = wrkWorkIpShare.getId();
//                        Long id1 = ipShare.getId();
//                    for (WrkWorkIpShareTree ipShare : assetShareList) {
//                        if(id1.equals(id)){
//                            wrkWorkIpShare.setIpShare(BigDecimal.ZERO);
//                            wrkWorkIpShare.setIpSocietyCode(ipShare.getIpSocietyCode());
//                        }
//                    }
//                    String rightType = wrkWorkIpShare.getRightType();
//                    List<WrkWorkIpShareTree> wrkWorkIpShareTreeList = noAssetIpShareMap.get(rightType);
//                    if(null == wrkWorkIpShareTreeList || wrkWorkIpShareTreeList.size()<1){
//                        continue;
//                    }
//                    for (WrkWorkIpShareTree ipShare : wrkWorkIpShareTreeList) {
//                        Long id1 = ipShare.getId();
//                        if(id1.equals(id)){
//                            wrkWorkIpShare.setIpShare(ipShare.getIpShare());
//                        }
//                    }
//
//                }
//            }
        }
    }


    /**
     *  计算成为公共财产的年份
     *  (計算年份 - 1 - deathYear)/ 50 - 1 >= 0 公共財產
     *  計算年份 - 1 - deathYear >= 50
     *  計算年份 >= deathYear + 51
     */
    private Integer calcCommonPropertyYear(MbrIp mbrIp){
        String deathYear = mbrIp.getDeathYear();
        if (StringUtils.isNotBlank(deathYear)) {
            int i_death_year = Integer.valueOf(deathYear);
            int i_dp_year = i_death_year + 51;
            return i_dp_year;
        }
        return null;

    }

    private boolean checkIsAssets(WrkWorkIpShareTree wrkWorkIpShare) {
        String year = wrkWorkIpShare.getYear();
        Map<String, MbrIp> mbrIpByIpBaseNoMap = wrkWorkIpShare.getWrkWorkIpSHareVO().getMbrIpByIpBaseNoMap();
        if(null == mbrIpByIpBaseNoMap || mbrIpByIpBaseNoMap.size()<1){
            return false;
        }
        String key = createKeyByWorkIpShareTree(wrkWorkIpShare);
        MbrIp mbrIp = mbrIpByIpBaseNoMap.get(key);
        if (null != mbrIp) {
            String deathYear = mbrIp.getDeathYear();
                if (StringUtils.isNotBlank(deathYear)) {
                    //計算公式：(計算年份 - 1 - deathYear)/ 50 - 1 >= 0 公共財產
                    //大於等於0是公版 -->SOC才為000
                    //小於0是非公版-->原本的SOC
                    int i_death_year = Integer.valueOf(deathYear);
                    int i_year = Integer.valueOf(year);
                    if(i_year - i_death_year >= 51){
                        return true;
                    }

                }
        }
        return false;
    }

    private boolean checkIsAssets1(WrkWorkIpShareTree wrkWorkIpShare) {
        String year = wrkWorkIpShare.getYear();
        Map<String, MbrIp> mbrIpByIpBaseNoMap = wrkWorkIpShare.getWrkWorkIpSHareVO().getMbrIpByIpBaseNoMap();
        if(null == mbrIpByIpBaseNoMap || mbrIpByIpBaseNoMap.size()<1){
            return false;
        }
        String key = createKeyByWorkIpShareTree(wrkWorkIpShare);
        MbrIp mbrIp = mbrIpByIpBaseNoMap.get(key);
        if (null != mbrIp) {
            String deathYear = mbrIp.getDeathYear();
            if (StringUtils.isNotBlank(deathYear)) {
                String deathMonth = mbrIp.getDeathMounth();
                String deathDay = mbrIp.getDeathDay();

                /*if (StringUtils.isBlank(deathMonth) && StringUtils.isBlank(deathDay)) {
                    deathMonth = "12";
                    deathDay = "31";
                }else if (StringUtils.isNotBlank(deathMonth) && StringUtils.isBlank(deathDay)) {
                    deathDay = "1";
                }else if (StringUtils.isBlank(deathMonth) && StringUtils.isNotBlank(deathDay)) {
                    deathMonth = "12";
                    deathDay = "31"; //第一种和第三种情况可以合并，当前不合并，方便阅读代码
                }*/

//                            String deathDateStr = deathYear + "-" + deathMonth + "-" + deathDay;
                try {
//                                Date deathDate = new SimpleDateFormat("yyyy-MM-dd").parse(deathDateStr);
                    Date deathDate = new SimpleDateFormat("yyyy").parse(deathYear);
                    Calendar c = Calendar.getInstance();
                    c.setTime(new SimpleDateFormat("yyyy").parse(year));
                    c.add(Calendar.YEAR, -50);// 当前时间减去50年
                    Date date = c.getTime();
//                    logger.info("入参的时间为："+year+",减去50年后的时间为："+date+";当前人死亡日期年份为："+deathDate+";date.compareTo(deathDate)是否大于等于0："+(date.compareTo(deathDate)>=0));
                    // 大于0 表示的死亡日期早于当前日期减去50年的时间，说明此人已挂
                    if (date.compareTo(deathDate) >= 0) {
                        return true;
                    }
                } catch (ParseException e) {
                    e.printStackTrace();
                }

            }
        }
        return false;
    }

    public List<WrkWorkIpShareTree> createWrkWorkIpShareTreeList(String genre,String workUniqueKey,List<WrkWorkIpShare> wwisList,
                                                                 String year,Long refWorkId,Integer refWrkWorkSoc, Boolean isDist) {

        HashMap<String, WrkWorkRight> wrkWorkRightByWrkWorkUniqueKeyMap = createWrkWorkRightByWrkWorkUniqueKey(workUniqueKey);

        if (null != wwisList && wwisList.size() > 0) {
            wwisList = wwisList.stream().filter(it -> it.getOipLinkId() != null && it.getOipLinkId() == 0).collect(Collectors.toList());
            if(null == wwisList || wwisList.size()<1){
                return new ArrayList<>();
            }
            List<WrkWorkIpShareTree> wrkWorkIpShareTreeList = new ArrayList<>();

            WrkWorkIpShareVO wrkWorkIpSHareVO = new WrkWorkIpShareVO();
            wrkWorkIpSHareVO.setRefWorkId(refWorkId);
            wrkWorkIpSHareVO.setRefWorkSoc(refWrkWorkSoc);

            try{
                Map<Object, Object> rightCache = redisService.getCache(List.class,"refRight"); //
                if(null == rightCache || rightCache.size()<1){
                    // 初始化 refRight  這裏應放入redis
                    List<RefRight> refRightList = refRightService.listAll();
                    if(null == refRightList || refRightList.size()<1){
                        return null;
                    }
                    Map<String,List<RefRight>> refRightMap = refRightList.stream().filter(it-> StringUtils.isNotBlank(it.getCashRight())).collect(Collectors.groupingBy(it ->it.getCashRight()));
                    redisService.addCache(List.class,refRightMap,"refRight");
                }

                Map<Object, Object> territoryRelationCache = redisService.getCache(List.class,"territoryRelation");
                if(null == territoryRelationCache || territoryRelationCache.size()<1){
                    List<RefTerritoryRelation> list = refTerritoryRelationService.listAll();
                    Map<String, List<RefTerritoryRelation>> mapList = list.stream().collect(Collectors.groupingBy(it->it.getTisN()+""));
                    redisService.addCache(List.class,mapList,"territoryRelation");
                }
            }catch (Exception exception){

            }

            try {
                //用於 回收年终截止合约剩余比例 <id, ipshare>

                Map<String, List<WrkWorkIpShare>> workIpShareMap = wwisList.stream().filter(it -> StringUtils.isNotBlank(it.getGroupIndicator())).collect(Collectors.groupingBy(it -> it.getGroupIndicator() + it.getRightType()));

                for (WrkWorkIpShare wrkWorkIpShare : wwisList) {
                    // 生成新的初始节点
                    WrkWorkIpShareTree wrkWorkIpShareTree = createWrkWorkIpShareTreeByIpShare(wrkWorkRightByWrkWorkUniqueKeyMap, wrkWorkIpShare, year, wwisList, genre);
                    wrkWorkIpShareTree.setDist(isDist);
                    // 这里需要放gp下的list
                    createIpNameNoListAndIpRole(workIpShareMap,wrkWorkIpShareTree);
                    // ipNameAndIpSoc
                    delWrkWorkIpShareNameAndIpBaseNoAndSoc(wrkWorkIpSHareVO, wrkWorkIpShareTree);

                    wrkWorkIpShareTree.setWrkWorkIpSHareVO(wrkWorkIpSHareVO);

                    wrkWorkIpShareTree.setAssignorList(new ArrayList<>());
                    // 构建该节点下的树结构，不做计算
                    createWrkWorkIpShareTree(wrkWorkIpShareTree); // TODO  继续优化
                    wrkWorkIpShareTreeList.add(wrkWorkIpShareTree);
                }

            } catch (Exception e) {
                logger.error(e.getMessage(),e);
                e.printStackTrace();
                return new ArrayList<>();
            }

            return wrkWorkIpShareTreeList;
        }
        return new ArrayList<>();
    }

    private void createIpNameNoListAndIpRole(Map<String, List<WrkWorkIpShare>> workIpShareMap, WrkWorkIpShareTree wrkWorkIpShareTree) {
        String key = wrkWorkIpShareTree.getGroupIndicator() + wrkWorkIpShareTree.getRightType();
        HashMap<String, List<String>> ipNameNoGpRightTypeMap = wrkWorkIpShareTree.getIpNameNoGpRightTypeMap();
        HashMap<String, Set<String>> workIpRoleGpRightTypeMap = wrkWorkIpShareTree.getWorkIpRoleGpRightTypeMap();
        if(null == ipNameNoGpRightTypeMap || ipNameNoGpRightTypeMap.size()<1){
            ipNameNoGpRightTypeMap = new HashMap<>();
        }
        if(null == workIpRoleGpRightTypeMap || workIpRoleGpRightTypeMap.size()<1){
            workIpRoleGpRightTypeMap = new HashMap<>();
        }

        List<WrkWorkIpShare> workIpShareList = workIpShareMap.get(key);

        if(null != workIpShareList && workIpShareList.size()>0){
            List<String> ipNameNos = ipNameNoGpRightTypeMap.get(key);
            if(null == ipNameNos || ipNameNos.size()<1){
                ipNameNoGpRightTypeMap.put(key,workIpShareList.stream().map(WrkWorkIpShare::getIpNameNo).collect(Collectors.toList()));
            }
            Set<String> workIpRoleSet = workIpRoleGpRightTypeMap.get(key);
            if(null == workIpRoleSet || workIpRoleSet.size()<1){
                workIpRoleGpRightTypeMap.put(key,workIpShareList.stream().map(WrkWorkIpShare::getWorkIpRole).collect(Collectors.toSet()));
            }
        }
        wrkWorkIpShareTree.setIpNameNoGpRightTypeMap(ipNameNoGpRightTypeMap);
        wrkWorkIpShareTree.setWorkIpRoleGpRightTypeMap(workIpRoleGpRightTypeMap);

    }

    @Override
    public WrkWorkIpShare delWrkWorkIpShareNameAndIpBaseNoAndSoc(WrkWorkIpShareVO wrkWorkIpShareVO, WrkWorkIpShare wrkWorkIpShare){
        WrkWorkIpShareTree wrkWorkIpShareTree = new WrkWorkIpShareTree();
        BeanUtils.copyProperties(wrkWorkIpShare,wrkWorkIpShareTree);
        // 根据ipNameNo校验 并赋值Name和ChineseName
        createIpNameMap(wrkWorkIpShareTree,wrkWorkIpShareVO.getMbrIpNameMap());

        // 给IpBaseNo赋值
        fuzhiIpBaseNo(wrkWorkIpShareTree,wrkWorkIpShareVO.getIpNameNoAndBaseNoMap());

        fuzhiIpSocietyCodeAndIpType(wrkWorkIpShareTree, wrkWorkIpShareVO.getMbrIpByIpBaseNoMap(),
                wrkWorkIpShareVO.getMbrIpAgreementHashMap(),wrkWorkIpShareVO.getMbrIpAgreementTerritoryMap(),
                wrkWorkIpShareVO.getIpNameNoAndBaseNoMap());

        WrkWorkIpShare workIpShare = new WrkWorkIpShare();
        BeanUtils.copyProperties(wrkWorkIpShareTree,workIpShare);
        return workIpShare;
    }

    private void delWrkWorkIpShareNameAndIpBaseNoAndSoc(WrkWorkIpShareVO wrkWorkIpShareVO, WrkWorkIpShareTree wrkWorkIpShareTree) {
        // 根据ipNameNo校验 并赋值Name和ChineseName
        createIpNameMap(wrkWorkIpShareTree,wrkWorkIpShareVO.getMbrIpNameMap());

        // 给IpBaseNo赋值
        fuzhiIpBaseNo(wrkWorkIpShareTree,wrkWorkIpShareVO.getIpNameNoAndBaseNoMap());

        fuzhiIpSocietyCodeAndIpType(wrkWorkIpShareTree, wrkWorkIpShareVO.getMbrIpByIpBaseNoMap(),
                wrkWorkIpShareVO.getMbrIpAgreementHashMap(),wrkWorkIpShareVO.getMbrIpAgreementTerritoryMap(),
                wrkWorkIpShareVO.getIpNameNoAndBaseNoMap());

    }

    private void fuzhiIpSocietyCodeAndIpTypeForDist(WrkWorkIpShareTree wrkWorkIpShareTree,Map<String,MbrIp> mbrIpByIpBaseNoMap,Map<String,List<MbrIpAgreement>> mbrIpAgreementHashMap,Map<String,List<MbrIpAgreementTerritory>> mbrIpAgreementTerritoryMap,Map<String,String> ipNameNoAndBaseNoMap) {

        String result = "";
        String ipSocietyCode = "099";
        String key = createKeyByWorkIpShareTree(wrkWorkIpShareTree); // ipNameNo+groupIndicator+rightType
        String ipBaseNo = ipNameNoAndBaseNoMap.get(key);
        String rightType = wrkWorkIpShareTree.getRightType();
        String year = wrkWorkIpShareTree.getYear();

        Calendar calendar = Calendar.getInstance();
        int  current_year = calendar.get(Calendar.YEAR) ;
        int dist_year = Integer.valueOf(year) ;
        Boolean isDist = wrkWorkIpShareTree.getDist();
        String calc_year = String.valueOf(current_year);

        boolean ipContainsCurrentWorkRight = false;
        MbrIp mbrIp = mbrIpByIpBaseNoMap.get(key);
        if(null == mbrIp){
            // 根据ipBaseNo查询 ip的权利
            if(StringUtils.isBlank(ipBaseNo)){
                wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
                return;
            }
            mbrIp = mbrIpService.getMbrIpByIpBaseNo(ipBaseNo);
            if(null == mbrIp){
                result = "當前ipBaseNo："+ipBaseNo+"對應MbrIp表查詢不到數據！";
                logger.info(result);
                mbrIpByIpBaseNoMap.put(key,new MbrIp());
                wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
                return;
            }
            String ipType = mbrIp.getIpType();
            wrkWorkIpShareTree.setIpType(ipType);
            mbrIpByIpBaseNoMap.put(key,mbrIp);

            Integer dpYear = calcCommonPropertyYear(mbrIp);
            if(dpYear != null){
                if(current_year >= dpYear && dist_year >= dpYear){ // 当前年度、分配年度均大于成为公共财产年度，公共财产 soc = 000
                    wrkWorkIpShareTree.setIpSocietyCode("000");
                    return;
                } else if(current_year >= dpYear && dist_year < dpYear ){ // 当前年度是公共财产，分配年度不是公共财产，如果是P/I分配，按着分配年度确认会籍
                    if(isDist){
                        calc_year = year;
                    } else {
                        wrkWorkIpShareTree.setIpSocietyCode("000");
                        return;
                    }
                }
            }

        }

        // PR权利有为0的 公共版权
        List<MbrIpAgreement> resultListAll = mbrIpAgreementService.getMbrIpAgreementsByIpBaseNo(ipBaseNo);
        List<MbrIpAgreement> commonList = resultListAll.stream().filter(r -> r.getSocietyCode().equals(0) && Constants.PR_RIGHT.contains(r.getRightCode()) ).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(commonList)){ //公共版权
            wrkWorkIpShareTree.setIpSocietyCode("000");
            return;
        }


        // 根據傳入的作品類型找到該類型下的子類型有哪些
        Map<Object, Object> rightCache = redisService.getCache(List.class,"refRight");
        List<RefRight> refRightList = JSON.parseArray(JSON.parseObject(JSON.toJSONString(rightCache, true)).get(rightType).toString(), RefRight.class);

        // 需要针对resultList 做ipSocietyCode的map，和ter里面查询出来的结果取交集
        Set<Integer> mbrIpAgreementIpSocietySet = new HashSet<>();
        List<String> rightCodeList = refRightList.stream().map(it -> it.getRightCode()).collect(Collectors.toList());
        List<MbrIpAgreement> mbrIpAgreementList = mbrIpAgreementHashMap.get(key);
        if(null == mbrIpAgreementList || mbrIpAgreementList.size()<1){
            List<MbrIpAgreement> resultList = mbrIpAgreementService.getMbrIpAgreementByIpBaseNoAndRightCodeList(ipBaseNo,rightCodeList,calc_year);
            if (null != resultList && resultList.size()>0){
                mbrIpAgreementIpSocietySet = resultList.stream().map(MbrIpAgreement::getSocietyCode).collect(Collectors.toSet());
                ipContainsCurrentWorkRight = true;
                mbrIpAgreementHashMap.put(key,resultList);
                mbrIpAgreementList = resultList;
            }
        }else{
            mbrIpAgreementIpSocietySet = mbrIpAgreementList.stream().map(MbrIpAgreement::getSocietyCode).collect(Collectors.toSet());
            ipContainsCurrentWorkRight = true;
        }

        boolean isContainsTWAndReleation = false;
        boolean isContinueTerr = false;
        ///20210420 data_source=N的表示是dummy ip ，不用管区域
        if (mbrIpAgreementList !=null){
            for (MbrIpAgreement mbrIpAgreement : mbrIpAgreementList) {
                if (StringUtils.equalsIgnoreCase(mbrIpAgreement.getDataSource(),"N")){
                    isContinueTerr = true;
                    isContainsTWAndReleation = true;
                    break;
                }
            }
        }

        Set<Integer> mbrIpAgreementTerritoriesIpSocietySet = new HashSet<>();
        if (!isContinueTerr ) {


            List<MbrIpAgreementTerritory> mbrIpAgreementTerritories = mbrIpAgreementTerritoryMap.get(key);
            if (null == mbrIpAgreementTerritories || mbrIpAgreementTerritories.size() < 1) {
                // 查看 此人所授权的区域是否包含台湾 TODO
                List<RefTerritoryRelation> refTerritoryRelationList = refTerritoryRelationService.selectTerrByRelTisN(158);
                List<Long> tisnList = refTerritoryRelationList.stream().map(RefTerritoryRelation::getTisN).collect(Collectors.toList());
                tisnList.add(158L);
                mbrIpAgreementTerritories = mbrIpAgreementTerritoryService.selectMbrIpAgreementTerrByIpBaseNoAndTisnListAndRightCodeList(ipBaseNo, tisnList, rightCodeList);
                if (null != mbrIpAgreementTerritories && mbrIpAgreementTerritories.size() > 0) {
                    mbrIpAgreementTerritoriesIpSocietySet = mbrIpAgreementTerritories.stream().map(MbrIpAgreementTerritory::getSocietyCode).collect(Collectors.toSet());
                    isContainsTWAndReleation = true;
                    mbrIpAgreementTerritoryMap.put(key, mbrIpAgreementTerritories);
                }
            } else {
                mbrIpAgreementTerritoriesIpSocietySet = mbrIpAgreementTerritories.stream().map(MbrIpAgreementTerritory::getSocietyCode).collect(Collectors.toSet());
                isContainsTWAndReleation = true;
            }
        }
//
//        Integer tisN = RefTerritoryRelationHandle.getTerritoryValidTime(territoryRelation, 158,
//                mbrIpAgreementTerritories);

        if (ipContainsCurrentWorkRight && isContainsTWAndReleation) {
            // 求交集
            Set<Integer> resultIpSocietyCode = new HashSet<>();
            resultIpSocietyCode.addAll(mbrIpAgreementIpSocietySet);
            if (!isContinueTerr){
                resultIpSocietyCode.retainAll(mbrIpAgreementTerritoriesIpSocietySet);
            }

            if(null == resultIpSocietyCode || resultIpSocietyCode.size()<1){
                wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
                return;
            }

            if(resultIpSocietyCode.size() > 1){
                mulitSoc(ipBaseNo,rightCodeList,wrkWorkIpShareTree);
                if(!CollectionUtils.isEmpty(wrkWorkIpShareTree.getMembershipShareMap())){
                    resultIpSocietyCode = wrkWorkIpShareTree.getMembershipShareMap().keySet();
                }
            }

            String ipSocietyCodeStr = StringUtils.join(resultIpSocietyCode, ";");// 以分号分割
            wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCodeStr);
        } else {
            wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
        }
    }

    /**
     * 会员会籍判断
     * mbr_ip_agreement: 只查creation_class_code = MW的数据
     *
     * 1.user定义的公共财产：有mbr_ip_agreement.society_code = 0，公共财产，soc=000
     *
     * 2.一般公共财产：P/I分配，以分配年度计算。 •計算公式：((分配年份-1-逝世年份)/50)-1
     *                                     大於等於0是公版 -->SOC才為000
     *                                     小於0是非公版-->原本的SOC
     *
     * 3.MUST会员。參考mbr_ip_agreement表的起迄日，查看对应right在分配当年mbr_ip_agreement.society_code是否为161
     *
     * 4.海外会员：以运行时间判断属于哪个协会。
     *
     * 5.所属协会是否是姊妹协会。看分配年度是否是姊妹协会，若是依该协会分配；若不是则不参加分配，soc不变
     *
     * @param wrkWorkIpShareTree
     * @param mbrIpByIpBaseNoMap
     * @param mbrIpAgreementHashMap
     * @param mbrIpAgreementTerritoryMap
     * @param ipNameNoAndBaseNoMap
     */

    private void fuzhiIpSocietyCodeAndIpType(WrkWorkIpShareTree wrkWorkIpShareTree,Map<String,MbrIp> mbrIpByIpBaseNoMap,
                                             Map<String,List<MbrIpAgreement>> mbrIpAgreementHashMap,
                                             Map<String,List<MbrIpAgreementTerritory>> mbrIpAgreementTerritoryMap,
                                             Map<String,String> ipNameNoAndBaseNoMap) {
        String result = "";
        String ipSocietyCode = "099";
        /*if(StringUtils.isBlank(wrkWorkIpShareTree.getIpNameNo())){
            wrkWorkIpShareTree.setIpSocietyCode(""); ;
            return;
        }*/
        String key = createKeyByWorkIpShareTree(wrkWorkIpShareTree);
        String ipBaseNo = ipNameNoAndBaseNoMap.get(key);
        String rightType = wrkWorkIpShareTree.getRightType();
        String year = wrkWorkIpShareTree.getYear();
        int dist_year = Integer.valueOf(year) ;

        boolean isDistYear = false;

        boolean ipContainsCurrentWorkRight = false;
        MbrIp mbrIp = mbrIpByIpBaseNoMap.get(key);
        if(null == mbrIp){
            // 根据ipBaseNo查询 ip的权利
            if(StringUtils.isBlank(ipBaseNo)){
                wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
                return;
            }
            mbrIp = mbrIpService.getMbrIpByIpBaseNo(ipBaseNo);
            if(null == mbrIp){
                result = "當前ipBaseNo："+ipBaseNo+"對應MbrIp表查詢不到數據！";
                logger.info(result);
                mbrIpByIpBaseNoMap.put(key,new MbrIp());
                wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
                return;
            }
            String ipType = mbrIp.getIpType();
            wrkWorkIpShareTree.setIpType(ipType);
            mbrIpByIpBaseNoMap.put(key,mbrIp);

            Integer dpYear = calcCommonPropertyYear(mbrIp);
            if(dpYear != null){
                Calendar calendar = Calendar.getInstance();
                int  current_year = calendar.get(Calendar.YEAR) ;
                if(current_year >= dpYear && dist_year >= dpYear){ // 当前年度、分配年度均大于成为公共财产年度，公共财产 soc = 000
                    wrkWorkIpShareTree.setIpSocietyCode("000");
                    return;
                } else if(current_year >= dpYear && dist_year < dpYear ){ // 当前年度是公共财产，分配年度不是公共财产，如果是P/I分配，按着分配年度确认会籍
                    if(wrkWorkIpShareTree.getDist()){
                        isDistYear = true; // soc要看分配當年年度
                    } else {
                        wrkWorkIpShareTree.setIpSocietyCode("000");
                        return;
                    }
                }
            }

        }

        // PR权利有为0的 公共版权
        List<MbrIpAgreement> resultListAll = mbrIpAgreementService.getMbrIpAgreementsByIpBaseNo(ipBaseNo);
        List<MbrIpAgreement> commonList = resultListAll.stream().filter(r -> r.getSocietyCode().equals(0)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(commonList)){ //公共版权
            wrkWorkIpShareTree.setIpSocietyCode("000");
            return;
        }

        // 根據傳入的作品類型找到該類型下的子類型有哪些
        Map<Object, Object> rightCache = redisService.getCache(List.class,"refRight");
        List<RefRight> refRightList = JSON.parseArray(JSON.parseObject(JSON.toJSONString(rightCache, true)).get(rightType).toString(), RefRight.class);

        // 需要针对resultList 做ipSocietyCode的map，和ter里面查询出来的结果取交集
        Set<Integer> mbrIpAgreementIpSocietySet = new HashSet<>();
        List<String> rightCodeList = refRightList.stream().map(it -> it.getRightCode()).collect(Collectors.toList());

        if( wrkWorkIpShareTree.getDist() && isMustCustomer(resultListAll,rightCodeList,dist_year) ){
            isDistYear = true;
        }

        List<MbrIpAgreement> mbrIpAgreementList = mbrIpAgreementHashMap.get(key);
        if(null == mbrIpAgreementList || mbrIpAgreementList.size()<1){
            List<MbrIpAgreement> resultList = new ArrayList<>();
            String ipRole = wrkWorkIpShareTree.getWorkIpRole();
           /* List<String> roleCodeList;
            if(!StringUtils.equals(ipRole,"PA")){
                roleCodeList = Constants.roleConvertMap.get(wrkWorkIpShareTree.getWorkIpRole());
            } else {
                roleCodeList = StringUtils.equals(wrkWorkIpShareTree.getIpType(),"L")?Constants.roleConvertMap.get("E"):Constants.roleConvertMap.get("CA");
            }*/
            if(isDistYear){
                resultList = mbrIpAgreementService.getMbrIpAgreementByIpBaseNoAndRightCodeList(ipBaseNo,rightCodeList,year) ; //看分配年度
            } else {  //看运行时，精确到天
                resultList = mbrIpAgreementService.getMbrIpAgreementByIpBaseAndRightCodeAndDate(ipBaseNo,rightCodeList,new Date()) ;
                if(wrkWorkIpShareTree.getDist() && !CollectionUtils.isEmpty(refRightList)){ // 一般分配
                    resultList = resultList.stream().filter(r -> !r.getSocietyCode().equals(161)).collect(Collectors.toList()); ;
                }
            }
            if (null != resultList && resultList.size()>0){
//                resultList = resultList.stream().filter(m -> roleCodeList.contains(m.getRoleCode())).collect(Collectors.toList());
                mbrIpAgreementIpSocietySet = resultList.stream().map(MbrIpAgreement::getSocietyCode).collect(Collectors.toSet());
                ipContainsCurrentWorkRight = true;
                mbrIpAgreementHashMap.put(key,resultList);
                mbrIpAgreementList = resultList;
            }
        }else{
            mbrIpAgreementIpSocietySet = mbrIpAgreementList.stream().map(MbrIpAgreement::getSocietyCode).collect(Collectors.toSet());
            ipContainsCurrentWorkRight = true;
        }

        boolean isContainsTWAndReleation = false;
        boolean isContinueTerr = false;
        ///20210420 data_source=N的表示是dummy ip ，不用管区域
        if (mbrIpAgreementList !=null){
            for (MbrIpAgreement mbrIpAgreement : mbrIpAgreementList) {
                if (StringUtils.equalsIgnoreCase(mbrIpAgreement.getDataSource(),"N")){
                    isContinueTerr = true;
                    isContainsTWAndReleation = true;
                    break;
                }
            }
        }

        Set<Integer> mbrIpAgreementTerritoriesIpSocietySet = new HashSet<>();
        if (!isContinueTerr ) {


            List<MbrIpAgreementTerritory> mbrIpAgreementTerritories = mbrIpAgreementTerritoryMap.get(key);
            if (null == mbrIpAgreementTerritories || mbrIpAgreementTerritories.size() < 1) {
                // 查看 此人所授权的区域是否包含台湾 TODO
                List<RefTerritoryRelation> refTerritoryRelationList = refTerritoryRelationService.selectTerrByRelTisN(158);
                List<Long> tisnList = refTerritoryRelationList.stream().map(RefTerritoryRelation::getTisN).collect(Collectors.toList());
                tisnList.add(158L);
                mbrIpAgreementTerritories = mbrIpAgreementTerritoryService.selectMbrIpAgreementTerrByIpBaseNoAndTisnListAndRightCodeList(ipBaseNo, tisnList, rightCodeList);
                if (null != mbrIpAgreementTerritories && mbrIpAgreementTerritories.size() > 0) {
                    mbrIpAgreementTerritoriesIpSocietySet = mbrIpAgreementTerritories.stream().map(MbrIpAgreementTerritory::getSocietyCode).collect(Collectors.toSet());
                    isContainsTWAndReleation = true;
                    mbrIpAgreementTerritoryMap.put(key, mbrIpAgreementTerritories);
                }
            } else {
                mbrIpAgreementTerritoriesIpSocietySet = mbrIpAgreementTerritories.stream().map(MbrIpAgreementTerritory::getSocietyCode).collect(Collectors.toSet());
                isContainsTWAndReleation = true;
            }
        }
//
//        Integer tisN = RefTerritoryRelationHandle.getTerritoryValidTime(territoryRelation, 158,
//                mbrIpAgreementTerritories);

        if (ipContainsCurrentWorkRight && isContainsTWAndReleation) {
            // 求交集
            Set<Integer> resultIpSocietyCode = new HashSet<>();
            resultIpSocietyCode.addAll(mbrIpAgreementIpSocietySet);
            if (!isContinueTerr){
                resultIpSocietyCode.retainAll(mbrIpAgreementTerritoriesIpSocietySet);
            }

            if(null == resultIpSocietyCode || resultIpSocietyCode.size()<1){
                wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
                return;
            }

            if(resultIpSocietyCode.size() > 1){
                mulitSoc(ipBaseNo,rightCodeList,wrkWorkIpShareTree);
                if(!CollectionUtils.isEmpty(wrkWorkIpShareTree.getMembershipShareMap())){
                    resultIpSocietyCode = wrkWorkIpShareTree.getMembershipShareMap().keySet();
                }
            }

            String ipSocietyCodeStr = StringUtils.join(resultIpSocietyCode, ";");// 以分号分割
            wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCodeStr);
        } else {
            wrkWorkIpShareTree.setIpSocietyCode(ipSocietyCode);
        }
    }

    private void fuzhiIpBaseNo(WrkWorkIpShareTree wrkWorkIpShareTree,Map<String,String> ipNameNoAndBaseNoMap) {
        String ipBaseNo = wrkWorkIpShareTree.getIpBaseNo();
        String ipNameNo = wrkWorkIpShareTree.getIpNameNo();
        String key = createKeyByWorkIpShareTree(wrkWorkIpShareTree);

        if (StringUtils.isBlank(ipBaseNo)) {
            String ipBaseNoValue = ipNameNoAndBaseNoMap.get(key);
            if(StringUtils.isBlank(ipBaseNoValue)){
                MbrIpNameMerge mbrIpNameMerge = mbrIpNameMergeService
                        .getIpNameMergeByNameNo(ipNameNo);
                if (null != mbrIpNameMerge) {
                    ipBaseNo = mbrIpNameMerge.getIpBaseNo();
                    wrkWorkIpShareTree.setIpBaseNo(ipBaseNo);
                    ipNameNoAndBaseNoMap.put(key,ipBaseNo);
                }else{
                    ipNameNoAndBaseNoMap.put(key,"");
                }
            }else{
                wrkWorkIpShareTree.setIpBaseNo(ipBaseNoValue);
            }
        }else{
            ipNameNoAndBaseNoMap.put(key,ipBaseNo);
            wrkWorkIpShareTree.setIpBaseNo(ipBaseNo);
        }
    }

    private String createKeyByWorkIpShareTree(WrkWorkIpShareTree wrkWorkIpShareTree) {
        String ipNameNo = wrkWorkIpShareTree.getIpNameNo();
        String groupIndicator = wrkWorkIpShareTree.getGroupIndicator();
        String rightType = wrkWorkIpShareTree.getRightType();
        return  ipNameNo+groupIndicator+rightType;
    }

    private HashMap<String, WrkWorkRight> createWrkWorkRightByWrkWorkUniqueKey(String workUniqueKey) {

        List<WrkWorkRight> wrkWorkRightByWorkUnique = wrkWorkRightService.getWrkWorkRightByWorkUnique(workUniqueKey);
        if(null != wrkWorkRightByWorkUnique && wrkWorkRightByWorkUnique.size()>0){
            HashMap<String, WrkWorkRight> wrkRightTypeMap = wrkWorkRightByWorkUnique.stream().collect(Collectors.toMap(WrkWorkRight::getRightType, Function.identity(), (key1, key2) -> key2, HashMap::new));
            return wrkRightTypeMap;
        }
        return null;
    }

    private void createWrkWorkIpShareTree(WrkWorkIpShareTree wrkWorkIpShareTree) {
        // 用于判断是否overleap合约
        wrkWorkIpShareTree.getAssignorList().add(wrkWorkIpShareTree);

        wrkWorkIpShareTree.setPreIpShare(wrkWorkIpShareTree.getIpShare());

        // 合约区域检验
        dealWrkWorkIpshareByDb(wrkWorkIpShareTree);

        if(null == wrkWorkIpShareTree.getAgrContentVOList() || wrkWorkIpShareTree.getAgrContentVOList().size()<1){
            return;
        }

        receiveTreeAgr(wrkWorkIpShareTree);

    }

    private void dealWrkWorkIpshareByDb(WrkWorkIpShareTree wrkWorkIpShareTree) {

        // 这里的ipType应该取 mbr_ip
//        String ipBaseNo = wrkWorkIpShareTree.getIpBaseNo();
        String ipNameNo = wrkWorkIpShareTree.getIpNameNo();
        String ipType = wrkWorkIpShareTree.getIpType();
        List<String> agrNoList = new ArrayList<>();
        Map<String,String> map = new HashMap<>() ;
        Map<String, Map<String,String>> ipBaseNoAgrNoMap = wrkWorkIpShareTree.getWrkWorkIpSHareVO().getIpBaseNoAgrNoMap()   ;
        // TODO 这里的ipType不应该为空，需要在根据作品Id查询的时候就赋值
//        if (StringUtils.isBlank(ipType)) {
//            ipType = "L";
//        }

        map = ipBaseNoAgrNoMap.get(ipNameNo);
        if(CollectionUtils.isEmpty(map)){
           map = agrAssignorService.getAgrNoByIpNameNo(ipNameNo);
           ipBaseNoAgrNoMap.put(ipNameNo,map) ;
        }

        agrNoList.addAll(map.keySet());

    /*    if(null ==agrNoList || agrNoList.size()<1){
//            agrNoList = agrAssignorService.selectAgrNoByIpNameNo(ipNameNo);
            ipBaseNoAgrNoList.put(ipNameNo,agrNoList);
        }
        if ("N".equalsIgnoreCase(ipType)) {
            // 查询个人下所有CONTENT_AGR_NO合约编号
            agrNoList = ipBaseNoAgrNoList.get(ipNameNo);
            if(null ==agrNoList || agrNoList.size()<1){
                agrNoList = agrAssignorService.selectAgrNoByIpNameNo(ipNameNo);
                ipBaseNoAgrNoList.put(ipNameNo,agrNoList);
            }
        }
        if ("L".equalsIgnoreCase(ipType)) {
            // 查询个人所有CONTENT_AGR_NO合约编号
            agrNoList = ipBaseNoAgrNoList.get(ipNameNo);
            if(null ==agrNoList || agrNoList.size()<1){
                agrNoList = agrAssignorService.selectAgrNoByIpNameNo(ipNameNo);
                ipBaseNoAgrNoList.put(ipNameNo,agrNoList);
            }
        }*/

        if (null ==agrNoList || agrNoList.size() == 0) {
            logger.debug("当前workIpShare的 ipBaseNo " + wrkWorkIpShareTree.getIpBaseNo() + "ipNameNo " + wrkWorkIpShareTree.getIpNameNo() + "在CONTENT_AGR_NO中没有合约不参与计算");
            return ;
        }
        List<AgrContentVO> agrContentVOList = wrkWorkIpShareTree.getAgrContentVOList();

        if(null == agrContentVOList || agrContentVOList.size()<1){
            getAgrContentVoList(wrkWorkIpShareTree, map);
        }

    }

    private WrkWorkIpShareTree createWrkWorkIpShareTreeByIpShare(HashMap<String, WrkWorkRight> wrkWorkRightByWrkWorkUniqueKeyMap,WrkWorkIpShare wrkWorkIpShare,String year,List<WrkWorkIpShare> wwisList,String genre) {
        WrkWorkIpShareTree wrkWorkIpShareTree = new WrkWorkIpShareTree();
        BeanUtils.copyProperties(wrkWorkIpShare,wrkWorkIpShareTree);
        wrkWorkIpShareTree.setWrkWorkRightMap(wrkWorkRightByWrkWorkUniqueKeyMap);
        wrkWorkIpShareTree.setYear(year);
        wrkWorkIpShareTree.setGenre(genre);
        wrkWorkIpShareTree.setInitWrkWorkIpShareList(wwisList);
        return wrkWorkIpShareTree;
    }

    private void getAgrContentVoList(WrkWorkIpShareTree wrkWorkIpShareTree, Map<String,String> agrNoMap) {

        List<AgrContentVO> agrContentVOList = concurrentHashMap.get(wrkWorkIpShareTree.getIpNameNo()+wrkWorkIpShareTree.getGroupIndicator()+wrkWorkIpShareTree.getRightType());
        if(null != agrContentVOList && agrContentVOList.size()>0){
            wrkWorkIpShareTree.setAgrContentVOList(agrContentVOList);
            return;
        }

//        ipNameNoList.add(wrkWorkIpShareTree.getIpNameNo());
        // 排除合约， extends g合约，是否排除作品，S合约是否包含作品
        List<String> ipNameNoList = new ArrayList<>();
        Set<String> workIpRoleSet = new HashSet<>();
        String key = wrkWorkIpShareTree.getGroupIndicator()+wrkWorkIpShareTree.getRightType();
        HashMap<String, List<String>> ipNameNoGpRightTypeMap = wrkWorkIpShareTree.getIpNameNoGpRightTypeMap();
        if(null != ipNameNoGpRightTypeMap && ipNameNoGpRightTypeMap.size()>0){
            ipNameNoList = ipNameNoGpRightTypeMap.get(key);
        }

        HashMap<String, Set<String>> workIpRoleGpRightTypeMap = wrkWorkIpShareTree.getWorkIpRoleGpRightTypeMap();
        if(null != workIpRoleGpRightTypeMap && workIpRoleGpRightTypeMap.size()>0){
            workIpRoleSet = workIpRoleGpRightTypeMap.get(key);
        }

        List<String> agrNoList = new ArrayList<>();
        agrNoList.addAll(agrNoMap.keySet());
        List<String> agrNoListByExtendWithoutExpired = agrContentService.getAgrNoListByExtendWithoutExpired(wrkWorkIpShareTree.getGenre(),wrkWorkIpShareTree.getWorkId(), wrkWorkIpShareTree.getWorkSocietyCode(), ipNameNoList, workIpRoleSet, agrNoList,wrkWorkIpShareTree.getYear(),wrkWorkIpShareTree.getWrkWorkIpSHareVO().getRefWorkId(),wrkWorkIpShareTree.getWrkWorkIpSHareVO().getRefWorkSoc());

        if (agrNoListByExtendWithoutExpired.size() == 0) {
            logger.debug("当前workIpShare的 ipBaseNo " + wrkWorkIpShareTree.getIpBaseNo() + "ipNameNo " + wrkWorkIpShareTree.getIpNameNo() + "在AgrContent extends中没有包含作品不参与计算");  // 如果没有合约，不计算
            return ;
        }

        List<AgrContentVO> agrContentVOS = new ArrayList<>();
        if (agrNoListByExtendWithoutExpired != null && agrNoListByExtendWithoutExpired.size() > 0) {
            agrContentVOS = agrContentService.selectByAgrNoListWithoutExpired(agrNoListByExtendWithoutExpired);
        }

        // 包含区域，排除区域
//        if (StringUtils.isBlank(tisn)) {
//            tisn = "TW";
//        }
        List<AgrContentVO> agrContentVOList1 = agrContentService.filterAgrContentVOByTerritory(agrContentVOS, "TW");

        if (agrContentVOList1.size() == 0) {
            logger.debug("当前workIpShare的 ipBaseNo " + wrkWorkIpShareTree.getIpBaseNo() + "ipNameNo " + wrkWorkIpShareTree.getIpNameNo() + "在AgrContent Territory中没有包括台湾区域不参与计算");
            return ;
        }
        // 合约年份处理
        // 勾选pre 往前找合约时间,查询当年的合约
//        if (StringUtils.isBlank(year)) {
//            year = new SimpleDateFormat("yyyy").format(new Date());
//        }
        List<AgrContentVO> agrContentVOList2 = agrContentService.filterAgrContentVOByYearForNew(wrkWorkIpShareTree.getYear(), agrContentVOList1);
        if (agrContentVOList2.size() == 0) {
            logger.debug("当前workIpShare的 ipBaseNo " + wrkWorkIpShareTree.getIpBaseNo() + "ipNameNo " + wrkWorkIpShareTree.getIpNameNo() + "在AgrContent根据年份进行空窗期合约处理没有符合条件的合约不参与计算");
            return ;
        }
        concurrentHashMap.put(wrkWorkIpShareTree.getIpNameNo()+wrkWorkIpShareTree.getGroupIndicator()+wrkWorkIpShareTree.getRightType(),agrContentVOList2);
        agrContentVOList2.forEach(a -> {if(agrNoMap.containsKey(a.getAgrNo())) a.setAssociatedAgrNo(agrNoMap.get(a.getAgrNo()));});
        wrkWorkIpShareTree.setAgrContentVOList(agrContentVOList2);
    }

    private void createIpNameMap(WrkWorkIpShareTree wrkWorkIpShareTree,Map<String,MbrIpName> mbrIpNameMap) {
        String ipNameNo = wrkWorkIpShareTree.getIpNameNo();
        String key = createKeyByWorkIpShareTree(wrkWorkIpShareTree);
        if (StringUtils.isBlank(ipNameNo)) {
            logger.debug("当前workIpShare的 ipBaseNo " + wrkWorkIpShareTree.getIpBaseNo() + " ipNameNo " + wrkWorkIpShareTree.getIpNameNo() + "没有值,不参与计算");
            return;
        }

        MbrIpName mbrIpName = mbrIpNameMap.get(key);
        if(null == mbrIpName || null == mbrIpName.getId()){
            mbrIpName = mbrIpNameService.getMbrIpNameByIpNameNo(ipNameNo);
            if(null == mbrIpName){
                logger.info("當前ipNameNo = "+ipNameNo+"；在數據庫表MbrIpName中無對應的數據！");
                return;
            }
            mbrIpNameMap.put(key,mbrIpName);
        }
        wrkWorkIpShareTree.setName(mbrIpName.getName());
        wrkWorkIpShareTree.setChineseName(mbrIpName.getChineseName());
    }

    public void receiveTreeAgr(WrkWorkIpShareTree wrkWorkIpShareTree){

        List<AgrContentVO> agrContentVOList = wrkWorkIpShareTree.getAgrContentVOList();

        if(null == agrContentVOList || agrContentVOList.size()<1){
            return;
        }

        for (AgrContentVO agrContentVO : agrContentVOList) {

            setAssignorIpShare(wrkWorkIpShareTree,agrContentVO);

            // assignee 处理
            dealTreeChild(wrkWorkIpShareTree,agrContentVO);

        }

    }

    private void setAssignorIpShare(WrkWorkIpShareTree wrkWorkIpShareTree,AgrContentVO agrContentVO) {
        String agrNo = agrContentVO.getAgrNo();
        String rightType = wrkWorkIpShareTree.getRightType();
        String key = agrNo+rightType;
        Map<String, BigDecimal> assignorRightTypeShare = wrkWorkIpShareTree.getAssignorRightTypeShare();
        if(null == assignorRightTypeShare || assignorRightTypeShare.size()<1){
            assignorRightTypeShare = new HashMap<>();
            assignorRightTypeShare.put(key,getRightTypeIpShare(rightType,agrContentVO));
        }else{
            BigDecimal bigDecimal = assignorRightTypeShare.get(rightType);
            if(null == bigDecimal){
                assignorRightTypeShare.put(key,getRightTypeIpShare(rightType,agrContentVO));
            }else{
                assignorRightTypeShare.put(key,bigDecimal);
            }
        }
        wrkWorkIpShareTree.setAssignorRightTypeShare(assignorRightTypeShare);
    }

    private BigDecimal getRightTypeIpShare(String rightType, AgrContentVO agrContentVO) {
        BigDecimal oipSshare = agrContentVO.getOipSshare();
        BigDecimal oipPshare = agrContentVO.getOipPshare();
        BigDecimal oipMshare = agrContentVO.getOipMshare();
        BigDecimal oipOdshare = agrContentVO.getOipOdshare();
        BigDecimal oipDbshare = agrContentVO.getOipDbshare();
        if ("PER".equals(rightType)) {
            return oipPshare;
        } else if ("MEC".equals(rightType)) {
            return oipMshare;
        } else if ("ZYN".equals(rightType)) {
            return oipSshare;
        } else if ("NOD".equals(rightType)) {
            return oipOdshare;
        } else if ("NDB".equals(rightType)) {
            return oipDbshare;
        }
        return null;
    }

    public void dealTreeChild(WrkWorkIpShareTree wrkWorkIpShareTree,AgrContentVO agrContent) {

        String agrNo = agrContent.getAgrNo();
        List<AgrAssignee> assigneeList = agrAssigneeService.getAssigneeByAgrNo(agrNo);
        if (null == assigneeList || assigneeList.size() == 0) {
            return ;
        }
        // 获取当前assignor下的assignee 第一次进来为空
        List<WrkWorkIpShareTree> bodyWrkWorkIpshareList = wrkWorkIpShareTree.getWorkIpShareList();
        if(null == bodyWrkWorkIpshareList || bodyWrkWorkIpshareList.size()<1){
            wrkWorkIpShareTree.setHasBody(false);
            bodyWrkWorkIpshareList = new ArrayList<>();
        }
        // 根据assigness带出ipType
        for (AgrAssignee agrAssignee : assigneeList) {
            // 根据agrAssignee 生成新的wrkWorkIpShareTree，当做当前wrkWorkIpShareTree的body
            WrkWorkIpShareTree bodyWrkWorkIpShareTree = createWrkWorkIpShareTreeByAgrAssignee(wrkWorkIpShareTree,agrAssignee,agrContent);

            // 如果是重复直接continue;
//            if(checkIsOverLeapIpShare(wrkWorkIpShareTree.getAssignorList(),bodyWrkWorkIpShareTree)){
//                continue;
//            }

            bodyWrkWorkIpShareTree.setWrkWorkIpSHareVO(wrkWorkIpShareTree.getWrkWorkIpSHareVO());
            bodyWrkWorkIpshareList.add(bodyWrkWorkIpShareTree);
            // 如果E是SD  就可以直接跳出循环了 且SD是由合約帶出來的  20201020 废弃
//            if("Y".equalsIgnoreCase(bodyWrkWorkIpShareTree.getSd()) && bodyWrkWorkIpShareTree.isAgrSd()){
//                continue;
//            }
            if(!agrContent.getOipNameNo().equals(agrAssignee.getSipNameNo())){
                createWrkWorkIpShareTree(bodyWrkWorkIpShareTree);
            }
        }
        wrkWorkIpShareTree.setWorkIpShareList(bodyWrkWorkIpshareList); // 存储子节点
        wrkWorkIpShareTree.setHasBody(true);
    }

    private boolean checkIsOverLeapIpShare(List<WrkWorkIpShareTree> assignorList, WrkWorkIpShareTree bodyWrkWorkIpShareTree) {
        boolean isOverLeap = false;
        for (WrkWorkIpShareTree wrkWorkIpShareTree : assignorList) {
            String ipNameNo = wrkWorkIpShareTree.getIpNameNo();
            String ipBaseNo = wrkWorkIpShareTree.getIpBaseNo();
            String rightType = wrkWorkIpShareTree.getRightType();
            String workIpRole = wrkWorkIpShareTree.getWorkIpRole();
            String groupIndicator = wrkWorkIpShareTree.getGroupIndicator();
            String agrNo = wrkWorkIpShareTree.getAgrNo();
            String key = ipNameNo+ipBaseNo+rightType+workIpRole+groupIndicator+agrNo;

            String bodyipNameNo = bodyWrkWorkIpShareTree.getIpNameNo();
            String bodyipBaseNo = bodyWrkWorkIpShareTree.getIpBaseNo();
            String bodyrightType = bodyWrkWorkIpShareTree.getRightType();
            String bodyworkIpRole = bodyWrkWorkIpShareTree.getWorkIpRole();
            String bodygroupIndicator = bodyWrkWorkIpShareTree.getGroupIndicator();
            String bodyagrNo = bodyWrkWorkIpShareTree.getAgrNo();
            String bodykey = bodyipNameNo+bodyipBaseNo+bodyrightType+bodyworkIpRole+bodygroupIndicator+bodyagrNo;

            if(key.equalsIgnoreCase(bodykey)){
                isOverLeap = true;
                break;
            }
        }
        return isOverLeap;
    }

    // 此处不涉及 计算
    private WrkWorkIpShareTree createWrkWorkIpShareTreeByAgrAssignee(WrkWorkIpShareTree wrkWorkIpShareTree,AgrAssignee agrAssignee,AgrContentVO agrContent) {
        WrkWorkIpShareTree bodyWrkWorkIpShareTree = new WrkWorkIpShareTree();
        bodyWrkWorkIpShareTree.setAgrNo(agrContent.getAgrNo());
        bodyWrkWorkIpShareTree.setAssociatedAgrNo(agrContent.getAssociatedAgrNo());
        String sipBaseNo = agrAssignee.getSipBaseNo();
        String sipNameNo = agrAssignee.getSipNameNo();

        bodyWrkWorkIpShareTree.setAgrContentVO(agrContent);
        bodyWrkWorkIpShareTree.setOipLinkId(wrkWorkIpShareTree.getSipLinkId());
        bodyWrkWorkIpShareTree.setGroupIndicator(wrkWorkIpShareTree.getGroupIndicator());
        bodyWrkWorkIpShareTree.setIpType(agrAssignee.getIpType());
        bodyWrkWorkIpShareTree.setIpBaseNo(sipBaseNo);
        bodyWrkWorkIpShareTree.setIpNameNo(sipNameNo);
        bodyWrkWorkIpShareTree.setWorkIpRole(agrAssignee.getSipRole());
        bodyWrkWorkIpShareTree.setRightType(wrkWorkIpShareTree.getRightType());
        bodyWrkWorkIpShareTree.setWorkId(wrkWorkIpShareTree.getWorkId());
        bodyWrkWorkIpShareTree.setWorkSocietyCode(wrkWorkIpShareTree.getWorkSocietyCode());
        bodyWrkWorkIpShareTree.setWorkUniqueKey(wrkWorkIpShareTree.getWorkUniqueKey());
        bodyWrkWorkIpShareTree.setOrgWriterShare(wrkWorkIpShareTree.getOrgWriterShare());
        bodyWrkWorkIpShareTree.setValidFr(agrContent.getAgrSdate());
        bodyWrkWorkIpShareTree.setValidTo(agrContent.getAgrEdate());
        bodyWrkWorkIpShareTree.setAutoExtensionInd(agrContent.getAutoExtensionInd());
        bodyWrkWorkIpShareTree.setInitWrkWorkIpShareList(wrkWorkIpShareTree.getInitWrkWorkIpShareList());
        bodyWrkWorkIpShareTree.setYear(wrkWorkIpShareTree.getYear());
        bodyWrkWorkIpShareTree.setIpShare(BigDecimal.ZERO.setScale(6,RoundingMode.HALF_UP));
        bodyWrkWorkIpShareTree.setGenre(wrkWorkIpShareTree.getGenre());
        bodyWrkWorkIpShareTree.setWorkIpRoleGpRightTypeMap(wrkWorkIpShareTree.getWorkIpRoleGpRightTypeMap());
        bodyWrkWorkIpShareTree.setIpNameNoGpRightTypeMap(wrkWorkIpShareTree.getIpNameNoGpRightTypeMap());
        bodyWrkWorkIpShareTree.setAssignorList(wrkWorkIpShareTree.getAssignorList());
        bodyWrkWorkIpShareTree.setDist(wrkWorkIpShareTree.getDist());

        // 如果是E且合约编号存在  表示的是合约带出来的  需要标记 writerOp为true
        if("E".equalsIgnoreCase(agrAssignee.getSipRole())){
            bodyWrkWorkIpShareTree.setWriterOp(true);
        }else{
            bodyWrkWorkIpShareTree.setWriterOp(false);
        }

        // 如果是SE的数据 需要设置 subPublisher为true
        if("SE".equalsIgnoreCase(agrAssignee.getSipRole())){
            bodyWrkWorkIpShareTree.setSubPublisher(true);
        }else{
            bodyWrkWorkIpShareTree.setSubPublisher(false);
        }


        // 检验重复问题
        Long sipLinkId = checkIsContainsCurrentIpshare(bodyWrkWorkIpShareTree,wrkWorkIpShareTree,sipNameNo);

        if (!bodyWrkWorkIpShareTree.isRepeat()) {
            try {
                sipLinkId = NoGener.getIncrement("sipLinkId");
            } catch (Exception e) {
                logger.error("Redis create sipLinkId exception ，this exception is " + e.getMessage());
            }
        }
        bodyWrkWorkIpShareTree.setSipLinkId(sipLinkId);

        setWrkWorkIpShareSdValue(bodyWrkWorkIpShareTree, wrkWorkIpShareTree, agrContent.getSd());

        delWrkWorkIpShareNameAndIpBaseNoAndSoc(wrkWorkIpShareTree.getWrkWorkIpSHareVO(), bodyWrkWorkIpShareTree);

        setAssigneeIpShare(bodyWrkWorkIpShareTree,agrAssignee);

        return bodyWrkWorkIpShareTree;
    }

    private void setAssigneeIpShare(WrkWorkIpShareTree bodyWrkWorkIpShareTree,AgrAssignee agrAssignee) {
        String agrNo = bodyWrkWorkIpShareTree.getAgrNo();
        String rightType = bodyWrkWorkIpShareTree.getRightType();
        String key = agrNo+rightType;
        Map<String, BigDecimal> assigneeRightTypeShare = bodyWrkWorkIpShareTree.getAssigneeRightTypeShare();
        if(null == assigneeRightTypeShare || assigneeRightTypeShare.size()<1){
            assigneeRightTypeShare = new HashMap<>();
            assigneeRightTypeShare.put(key,getRightTypeIpShareByAssignee(rightType,agrAssignee,bodyWrkWorkIpShareTree.getDist()));
        }else{
            BigDecimal bigDecimal = assigneeRightTypeShare.get(rightType);
            if(null == bigDecimal){
                assigneeRightTypeShare.put(key,getRightTypeIpShareByAssignee(rightType,agrAssignee,bodyWrkWorkIpShareTree.getDist()));
            }else{
                assigneeRightTypeShare.put(key,bigDecimal);
            }
        }
        bodyWrkWorkIpShareTree.setAssigneeRightTypeShare(assigneeRightTypeShare);
    }

    private BigDecimal getRightTypeIpShareByAssignee(String rightType, AgrAssignee agrAssignee, boolean isDist) {
        BigDecimal sipSshare = agrAssignee.getSipSshare();
        BigDecimal sipPshare = agrAssignee.getSipPshare();
        BigDecimal sipMshare = agrAssignee.getSipMshare();
        BigDecimal sipOdshare = agrAssignee.getSipOdshare();
        BigDecimal sipDbshare = agrAssignee.getSipDbshare();
        if ("PER".equals(rightType)) {
            return sipPshare;
        } else if ("MEC".equals(rightType)) {
            return sipMshare;
        } else if ("ZYN".equals(rightType)) {
            return sipSshare;
        } else if ("NOD".equals(rightType)) {
            if(isDist) return sipPshare;
            return sipOdshare;
        } else if ("NDB".equals(rightType)) {
            if(isDist) return sipMshare;
            return sipDbshare;
        }
        return null;
    }

    private Long checkIsContainsCurrentIpshare(WrkWorkIpShareTree assignee,WrkWorkIpShareTree assignor,String sipNameNo) {
        Long sipLinkId = 0L;
        List<WrkWorkIpShare> initWrkWorkIpShareList = assignor.getInitWrkWorkIpShareList();
        for (WrkWorkIpShare wrkWorkIpShare : initWrkWorkIpShareList) {
            String groupIndicator = wrkWorkIpShare.getGroupIndicator();
            String rightType = wrkWorkIpShare.getRightType();
            String ipNameNo = wrkWorkIpShare.getIpNameNo();
            String initIpShareKey =  ipNameNo+rightType+groupIndicator;
            String currentIpShareKey = sipNameNo+assignee.getRightType()+assignee.getGroupIndicator();
            if(initIpShareKey.equalsIgnoreCase(currentIpShareKey)){
                sipLinkId = wrkWorkIpShare.getSipLinkId();
                assignee.setSd(wrkWorkIpShare.getSd());
                assignee.setRepeat(true);
//                BigDecimal assignorIpShare = assignor.getIpShare();
//                if(null == assignorIpShare){
//                    assignorIpShare = BigDecimal.ZERO;
//                }
//                BigDecimal initIpShare = wrkWorkIpShare.getIpShare();
//                if(null == initIpShare){
//                    initIpShare = BigDecimal.ZERO;
//                }
//                assignorIpShare = assignorIpShare.add(initIpShare);
//                assignor.setIpShare(assignorIpShare);
//                assignor.setPreIpShare(assignorIpShare);
            }
        }
        return sipLinkId;
    }

    // 确认逻辑： 1、当wrkWorkRight 中有权利关系的时候，以此为准,该权利下所有ipshare都应该勾选SD
    // 2、没有权利关系的时候，当前ipshare或者是合约勾选了 ipshare都要勾选SD
    private void setWrkWorkIpShareSdValue(WrkWorkIpShareTree bodyWrkWorkIpShareTree,WrkWorkIpShareTree assignor,String agrSd) {
        String rightType = bodyWrkWorkIpShareTree.getRightType();
        if(StringUtils.isNotBlank(rightType)){
            Map<String, WrkWorkRight> wrkWorkRightMap = assignor.getWrkWorkRightMap();
            if(null != wrkWorkRightMap && wrkWorkRightMap.size()>0){
                WrkWorkRight wrkWorkRight = wrkWorkRightMap.get(rightType);
                if(null != wrkWorkRight){
                    String workSd = wrkWorkRight.getWorkSd();
                    if("Y".equalsIgnoreCase(workSd)){
                        bodyWrkWorkIpShareTree.setSd(workSd);
                        assignor.setSd(workSd);
                    }else{
                        dealWithSd(bodyWrkWorkIpShareTree, assignor, agrSd);
                    }
                }else{
                    // 不是重複的 false 表示带出来的e属于新的 不存在数据库中 以合约为准
                    dealWithSd(bodyWrkWorkIpShareTree, assignor, agrSd);
                }
            }else{
                // 如果work没有勾选sd那么下面的数据根据当前人一直往下延伸
                dealWithSd(bodyWrkWorkIpShareTree, assignor, agrSd);

            }
        }
    }

    private void dealWithSd(WrkWorkIpShareTree bodyWrkWorkIpShareTree, WrkWorkIpShareTree assignor, String agrSd) {
        String sd = assignor.getSd();
        if("Y".equalsIgnoreCase(sd)){
            bodyWrkWorkIpShareTree.setSd(sd);
        }else{
            if("Y".equalsIgnoreCase(agrSd)){
                bodyWrkWorkIpShareTree.setSd(agrSd);
                // ca和e之間有sd ,ca不勾sd
                if(Constants.WORK_IP_ROLE_ORIGNIAL.contains(assignor.getWorkIpRole()) && Constants.WORK_IP_ROLE_COMPANY.contains(bodyWrkWorkIpShareTree.getWorkIpRole())){
                    assignor.setSd("N");
                }else {
                    assignor.setSd(agrSd);
                }
            }else{
                bodyWrkWorkIpShareTree.setSd(agrSd);
            }
        }
    }

    // 排序
    private List<WrkWorkIpShare> sortByWorkIpRole(List<WrkWorkIpShare> perList) {
        if (null == perList || perList.size() == 0){
            return new ArrayList<>();
        }

        Map<String, List<WrkWorkIpShare>> map = perList.stream().filter(per -> StringUtils.isNotBlank(per.getGroupIndicator()))
                .collect(Collectors.groupingBy(t -> t.getGroupIndicator()));
        List<WrkWorkIpShare> noGroupIndiList = perList.stream().filter(it -> StringUtils.isBlank(it.getGroupIndicator())).collect(Collectors.toList());
        map.put("", noGroupIndiList);
        if (map.isEmpty()){
            return perList;
        }

        List<WrkWorkIpShare> sortWwisList = new ArrayList<>();
        /* 然后再对map处理，这样就方便取出自己要的数据 */
        Map<String, Integer> mapWorkIpRoleSort = new HashMap<String, Integer>();
        mapWorkIpRoleSort.put("CA", 1);
        mapWorkIpRoleSort.put("C", 2);
        mapWorkIpRoleSort.put("A", 3);
        mapWorkIpRoleSort.put("PA", 4);
        mapWorkIpRoleSort.put("E", 5);
        mapWorkIpRoleSort.put("SE", 6);
//	    try {
        for (Map.Entry<String, List<WrkWorkIpShare>> entry : map.entrySet()) {
            List<WrkWorkIpShare> wwisList = entry.getValue();
            if(null == wwisList || wwisList.size()<1){
                continue;
            }
            Collections.sort(wwisList,(o1,o2)->{
                String a = o1.getWorkIpRole();
                String b = o2.getWorkIpRole();
                if(a == null && b == null) {
                    return 0;
                }
                if(a == null) {
                    return -1;
                }
                if(b == null) {
                    return 1;
                }
                if (null == mapWorkIpRoleSort.get(a) && null == mapWorkIpRoleSort.get(b)) {
                    return 0;
                }

                if (null == mapWorkIpRoleSort.get(a)) {
                    return -1;
                }

                if (null == mapWorkIpRoleSort.get(b)) {
                    return 1;
                }

                return mapWorkIpRoleSort.get(a).compareTo(mapWorkIpRoleSort.get(b));

            });
           /* wwisList = wwisList.stream().filter(t -> t.getWorkIpRole() != null)
                    .sorted(Comparator.comparing(WrkWorkIpShare::getWorkIpRole, (a, b) -> {
                        if(a == null && b == null) {
                            return 0;
                        }
                        if(a == null) {
                            return -1;
                        }
                        if(b == null) {
                            return 1;
                        }
                        if (null == mapWorkIpRoleSort.get(a) || null == mapWorkIpRoleSort.get(b)) {
                            return 0;
                        }
                        return mapWorkIpRoleSort.get(a).compareTo(mapWorkIpRoleSort.get(b));
                    })).collect(Collectors.toList());*/
            sortWwisList.addAll(wwisList);
        }
        return sortWwisList;
    }

    private List<WrkWorkIpShare> createWrkWorkIpShare(List<WrkWorkIpShare> wrkWorkIpShares, WrkWork wrkWork){
        Set<Long> oiplinkWrkWorkIpShareSet = wrkWorkIpShares.stream().filter(w -> (w.getOipLinkId() != null && w.getOipLinkId() != 0)).map(WrkWorkIpShare :: getOipLinkId).collect(Collectors.toSet());
        Map<Long, Long> oipLinkMap = new HashMap<>() ; // oipLink和sipLink新的对应关系
        List<WrkWorkIpShare> results = new ArrayList<>() ;

        for(WrkWorkIpShare workIpShare : wrkWorkIpShares){
            WrkWorkIpShare newWorkIpShare = new WrkWorkIpShare();
            BeanUtils.copyProperties(workIpShare,newWorkIpShare);
            workIpShare.setId(null);
            workIpShare.setWorkId(wrkWork.getWorkId());
            workIpShare.setWorkSocietyCode(wrkWork.getWorkSocietyCode());
            workIpShare.setWorkUniqueKey(wrkWork.getWorkUniqueKey());
            workIpShare.setRefIndicator("Y");
            try {
                Long oldSipLinkId = workIpShare.getSipLinkId() ;
                Long sipLinkId = NoGener.getIncrement("sipLinkId");
                workIpShare.setSipLinkId(sipLinkId);
                if(oiplinkWrkWorkIpShareSet.contains(oldSipLinkId)){ // 有oip指向当前sip
                    oipLinkMap.put(oldSipLinkId,sipLinkId);
                }
            } catch (Exception e) {
                logger.error("Redis create sipLinkId exception ，this exception is " + e.getMessage());
            }
            results.add(workIpShare) ;
        }

        //修改oip指向
        results.forEach(w-> {
            Long oipLinkId = w.getOipLinkId() ;
            Long newOipLinkId = oipLinkMap.get(oipLinkId) ;
            if(oipLinkId != 0 && newOipLinkId != null){
                w.setOipLinkId(newOipLinkId);
            }
        });

        return results;
    }

    /**
     * 会员有多个协会时比例拆分，分配计算时用到，画面不拆分
     * 1.根据ip_role去ref_role_convert表找对应的role_code
     * 2.根据right_code、role_code查询mbr_ip_agreement，creation_class_code固定为MW
     * 3.按mbr_ip_agreement.membership_share拆分
     * 4.A、C只对应一个role_code  C:MC，A:LY
     * 5.CA、PA对应多个role_code CA:MC、LY;PA:MC、LY、EM
     * 6.CA看成C和A2个role，CA的比例等比例拆分，A的比例：shareA*LY=share/2*LY,C的比例：shareC*MC=share/2*MC;
     * 7.PA要看ip type，团体会员对应EM，个人和CA一样
     *
     *
     * @param ipBaseNo
     * @param rightCodeList
     * @param wrkWorkIpShareTree
     */
    public void mulitSoc(String ipBaseNo, List<String> rightCodeList,WrkWorkIpShareTree wrkWorkIpShareTree){
        List<MbrIpAgreement> mbrIpAgreementListCurr = mbrIpAgreementService.getMbrIpAgreementByIpBaseNoAndRightCodeList(ipBaseNo, rightCodeList,null);
        if(!CollectionUtils.isEmpty(mbrIpAgreementListCurr)){
            Map<Integer,List<MbrIpAgreement>> mbrIpAgreementMap= mbrIpAgreementListCurr.stream().filter(m -> !m.getSocietyCode().equals(161)).collect(Collectors.groupingBy(MbrIpAgreement :: getSocietyCode));
            if(mbrIpAgreementMap.size() == 1){
                Map<Integer,BigDecimal> membershipShareMap = new HashMap<>();
                membershipShareMap.put(mbrIpAgreementListCurr.get(0).getSocietyCode() , BigDecimal.ONE) ;
                wrkWorkIpShareTree.setMembershipShareMap(membershipShareMap);
            }else {
                Map<Integer,BigDecimal> membershipShareMap = multicSoc(wrkWorkIpShareTree,mbrIpAgreementListCurr);
                wrkWorkIpShareTree.setMembershipShareMap(membershipShareMap);
            }
        }
    }

    public Map<Integer,BigDecimal> multicSoc(WrkWorkIpShareTree wrkWorkIpShareTree,List<MbrIpAgreement> mbrIpAgreementList){

        List<String> stringList = Constants.roleConvertMap.get(wrkWorkIpShareTree.getWorkIpRole());

        BigDecimal share = BigDecimal.ONE;
        if(wrkWorkIpShareTree.getWorkIpRole().equals("CA")){
            share = share.multiply(new BigDecimal(0.5));

        } else if(StringUtils.equals(wrkWorkIpShareTree.getWorkIpRole(),"PA")){
            if(StringUtils.equals(wrkWorkIpShareTree.getIpType(),"N")){
                share = share.multiply(new BigDecimal(0.5));
                stringList = Constants.roleConvertMap.get("CA");
            } else if(StringUtils.equals(wrkWorkIpShareTree.getIpType(),"L")){
                stringList = Constants.roleConvertMap.get("E");
            }

        }

        Map<Integer,BigDecimal> socShareMap = new HashMap<>() ;
        for(MbrIpAgreement mbrIpAgreement : mbrIpAgreementList){
            if(!stringList.contains(mbrIpAgreement.getRoleCode())){
                continue;
            }
            Integer soc = mbrIpAgreement.getSocietyCode();
            BigDecimal socShare = socShareMap.getOrDefault(soc, BigDecimal.ZERO) ;
            BigDecimal toShare = share.multiply(new BigDecimal(mbrIpAgreement.getMembershipShare())).multiply(new BigDecimal(0.01)) ;
            socShareMap.put(soc,socShare.add(toShare)) ;
        }

        return socShareMap;
    }

    /**
     *  是否MUST会员
     *  如果分配当年是会员，看分配当年
     *  如果分配当年不是会员看当下，当下是会员的去分配当年
     *
     * @param resultListAll
     * @param rightCodeList
     * @return
     */
    public boolean isMustCustomer(List<MbrIpAgreement> resultListAll, List<String> rightCodeList, int year){

//        int year = DateUtils.getYear();
        for(MbrIpAgreement mbrIpAgreement : resultListAll){
            if(!rightCodeList.contains(mbrIpAgreement.getRightCode())){
                continue;
            }

            int from = DateUtils.getYear(mbrIpAgreement.getValidFrom()) ;
            int to = DateUtils.getYear(mbrIpAgreement.getValidTo());

            if(from <= year && to >= year && mbrIpAgreement.getSocietyCode().equals(161)){
                return true;
            }
        }

        return false;
    }

    private WrkWorkIpShare changeToWrkWorkIpShare(WrkWorkIpShareTree wrkWorkIpShare) {
        WrkWorkIpShare workIpShare = new WrkWorkIpShare();
        BeanUtils.copyProperties(wrkWorkIpShare,workIpShare);
//        StringBuilder result = new StringBuilder();
//        String groupIndicator = workIpShare.getGroupIndicator();
//        String rightType = workIpShare.getRightType();
//        Long oipLinkId = workIpShare.getOipLinkId();
//        Long sipLinkId = workIpShare.getSipLinkId();
//        String ipNameNo = workIpShare.getIpNameNo();
//        String ipBaseNo = workIpShare.getIpBaseNo();
//        String agrNo = workIpShare.getAgrNo();
//        Date validFr = workIpShare.getValidFr();
//        String validFrom = "";
//        if(null != validFr){
//            validFrom = new SimpleDateFormat("yyyy-MM-dd").format(validFr);
//        }
//        String validToStr = "";
//        Date validTo = workIpShare.getValidTo();
//        if(null != validTo){
//            validToStr = new SimpleDateFormat("yyyy-MM-dd").format(validTo);
//        }
//        String sd = workIpShare.getSd();
//        Integer ipSocietyCode = workIpShare.getIpSocietyCode();
//        String name = workIpShare.getName();
//        BigDecimal ipShare = workIpShare.getIpShare();
//        result.append("- - 當前IpShare == GP:").append(groupIndicator);
//        result.append(" == rightType:").append(rightType);
//        result.append(" == oipLinkId:").append(oipLinkId);
//        result.append(" == sipLinkId:").append(sipLinkId);
//        result.append(" == ipNameNo:").append(ipNameNo);
//        result.append(" == ipBaseNo:").append(ipBaseNo);
//        result.append(" == agrNo:").append(agrNo);
//        result.append(" == 合約開始時間 validFr:").append(validFrom);
//        result.append(" == 合約結束時間 validTo:").append(validToStr);
//        result.append(" == sd:").append(sd);
//        result.append(" == ipSocietyCode:").append(ipSocietyCode);
//        result.append(" == name:").append(name);
//        result.append(" == ipShare:").append(ipShare);
//        logger.info(result.toString());
        return workIpShare;
    }


}
