package tw.org.must.must.core.service.dist.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;
import tw.org.must.must.core.service.dist.*;
import tw.org.must.must.core.service.distdata.DistDataCalcWorkIpRoyService;
import tw.org.must.must.core.service.mbr.*;
import tw.org.must.must.core.service.ref.RefBankService;
import tw.org.must.must.core.service.ref.RefMemberTaxRateService;
import tw.org.must.must.core.service.ref.RefPoolMstrService;
import tw.org.must.must.core.service.ref.RefSocietyTaxRateService;
import tw.org.must.must.model.dist.*;
import tw.org.must.must.model.distdata.DistDataCalcWorkIpRoy;
import tw.org.must.must.model.mbr.*;
import tw.org.must.must.model.mbr.vo.IpNameVO;
import tw.org.must.must.model.ref.RefBank;
import tw.org.must.must.model.ref.RefSociety;
import tw.org.must.must.model.ref.RefSocietyRight;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static tw.org.must.must.common.constants.Constants.BATCH_SIZE_100;
import static tw.org.must.must.core.aspect.AsyncTaskAspect.log;

@Service
@Scope("prototype")
public class DistParamInfoRetainCalImpl implements DistParamInfoRetainCalService {

    @Autowired
    private DistDataCalcWorkIpRoyService distDataCalcWorkIpRoyService;
    @Autowired
    private MbrMemberMembershipService mbrMemberMembershipService;
    @Autowired
    private DistCalcRetainService distCalcRetainService;
    @Autowired
    private DistRoyMbrTranDtlService distRoyMbrTranDtlService;
    @Autowired
    private DistUpaParamService distUpaParamService;
    @Autowired
    private DistRoySocTranDtlService distRoySocTranDtlService;
    @Autowired
    private MbrMemberBankAccService mbrMemberBankAccService;
    @Autowired
    private MbrMemberInfoService mbrMemberInfoService;
    @Autowired
    private MbrIpNameService mbrIpNameService;
    @Autowired
    private MbrMemberSuccessorService mbrMemberSuccessorService;
    @Autowired
    private RefPoolMstrService refPoolMstrService;
    @Autowired
    private DistParamSourceService distParamSourceService;
    @Autowired
    private RefSocietyTaxRateService refSocietyTaxRateService;
    @Autowired
    private RefMemberTaxRateService refMemberTaxRateService;
    @Autowired
    private MbrIpNationalityService mbrIpNationalityService;

    @Autowired
    private RefBankService refBankService;

    @Autowired
    private MbrIpService mbrIpService;

    @Autowired
    private MbrIpAgreementService mbrIpAgreementService;

    /**********************************************************/
    private DistParamInfo distParamInfo;
    private DistUpaParam distUpaParam;
    private Map<String, BigDecimal> refSocietyTaxRateMap; // 海外籍 汇率 <countryCode, taxRate>
    private Map<Integer, BigDecimal> refMemberTaxRateMap; // 海外籍 汇率 <countryCode, taxRate>
    private Map<String, RefSocietyRight> allSocietyRightMap;
    private Map<String, List<DistRoyMbrTranDtl>> distRoyMbrTranDtlIpBaseNoMap;
    private Map<String, List<DistRoySocTranDtl>> distRoySocTranDtlSocietyCodeMap;

    private Map<String, DistDataCalcWorkIpRoy> ipBaseNoMap;

    private Map<String, MbrMemberBankAcc> mbrMemberBankAccMap;

    private Map<String, MbrMemberInfo> mbrMemberInfoMap;

    private Map<String, MbrMemberMembership> mbrMemberMembershipMap;

    private Map<String, MbrIpName> ipNameMap;
    private Map<String, IpNameVO> paNameMap;
    private Map<String, MbrIp> mbrIpMap;

    private Map<Integer, RefSociety> allSocietyMap;
    private Map<String, MbrMemberSuccessor> mbrMemberSuccessorMap;
    private Map<String, String> refPoolMstrMap;
    private Map<Long,String> allHeadBankMap;

    /**********************************************************/

    @Override
    public void init(DistParamInfo distParamInfo, Map<String, RefSocietyRight> allSocietyRightMap, Map<Integer, RefSociety> allSocietyMap) {
        this.distParamInfo = distParamInfo;
        this.allSocietyRightMap = allSocietyRightMap;
        this.allSocietyMap = allSocietyMap;
    }

    @Override
    public List<String> calDistParamInfoRetain() {
        List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getDistDataCalcWorkIpRoyListByDistNo(distParamInfo.getDistNo());
        if(distDataCalcWorkIpRoyList.isEmpty()) {
            return null; // 空 就不做处理，会各种报错
        }

        List<String> souceTypes = distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getSourceType).distinct().collect(Collectors.toList());

        // 清理 distCalcRetain
        clearDistCalcRetain(distParamInfo.getDistNo());

        //161会员根据ipbaseno分组 非161根据ipsoc分组
        Set<String> notMustGroup = new HashSet<>();
        Map<String, List<DistDataCalcWorkIpRoy>> mustGroup = distDataCalcWorkIpRoyList.stream().filter(r ->  StringUtils.isNotBlank(r.getIpBaseNo()) && StringUtils.isNotBlank(r.getIpSocietyCode()) && !StringUtils.equalsAny(r.getIpSocietyCode(),"099","000"))
                .collect(Collectors.groupingBy(d -> {
                    if(StringUtils.equals(d.getIpSocietyCode(),"161")){
                        return d.getIpBaseNo();
                    }else {
                        if(!notMustGroup.contains(d.getIpSocietyCode())){
                            notMustGroup.add(d.getIpSocietyCode());
                        }
                        return d.getIpSocietyCode();
                    }
                }));

        // 初始化数据
        initData(distDataCalcWorkIpRoyList);

        List<String> ipbaseAndSoc = this.mbrMemberInfoMap.keySet().stream().collect(Collectors.toList());
        ipbaseAndSoc.addAll(notMustGroup);
        for (String ipBaseNo : ipbaseAndSoc) {
            log.info("ipbaseno1:{}", ipBaseNo);
            if(!notMustGroup.contains(ipBaseNo)){ //当前报表中没有的会员也要导出，会员状态 3表示已删除
                MbrMemberInfo mbrMemberInfo = mbrMemberInfoMap.get(ipBaseNo);
                if(mbrMemberInfo == null || mbrMemberInfo.getStatus() == null ||mbrMemberInfo.getStatus() == 3){
                    continue;
                }

                List<MbrIpAgreement> mbrIpAgreementList = mbrIpAgreementService.getMbrIpAgreementByIpBaseNoAndRightCodeList(ipBaseNo,null,distParamInfo.getTaxYear()) ;
                if(CollectionUtils.isEmpty(mbrIpAgreementList)){
                    continue;
                }
            }else { //协会
                RefSocietyRight societyRight = allSocietyRightMap.get(ipBaseNo + "PER");
                if(societyRight == null){ //非姐妹协会不输出  TODO 没有根据权利类型取值
                    continue;
                }
            }
            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyIpList = mustGroup.get(ipBaseNo);
            DistCalcRetain distCalcRetain = getDistCalcRetain(ipBaseNo, distDataCalcWorkIpRoyIpList,notMustGroup.contains(ipBaseNo));
            if(distCalcRetain.getIpType().equals("S") && distCalcRetain.getSubTotalRoy().compareTo(BigDecimal.ZERO) == 0){
                continue;
            }

            distCalcRetainService.add(distCalcRetain);
            if (distDataCalcWorkIpRoyIpList != null && Objects.nonNull(distCalcRetain.getId()) && distCalcRetain.getId() > 0 ) {
                distDataCalcWorkIpRoyIpList.forEach(x -> x.setRetainId(distCalcRetain.getId()));
                List<List<DistDataCalcWorkIpRoy>> partitions = Lists.partition(distDataCalcWorkIpRoyIpList, BATCH_SIZE_100);
                for(List<DistDataCalcWorkIpRoy> partition : partitions){
                    distDataCalcWorkIpRoyService.updateBatchByPrimaryKeySelective(partition);
                }
            }
        }

        distDataCalcWorkIpRoyList.clear();
//        this.ipBaseNoMap.clear();
//        this.distRoyMbrTranDtlIpBaseNoMap.clear();
//        this.distRoySocTranDtlSocietyCodeMap.clear();
//        this.ipNameMap.clear();
//        this.paNameMap.clear();
//        this.mbrIpMap.clear();
//        this.mbrMemberMembershipMap.clear();
//        this.mbrMemberBankAccMap.clear();
//        this.mbrMemberSuccessorMap.clear();
        return souceTypes;
    }


    public List<String> calDistParamInfoRetain1(){

        // 清理 distCalcRetain
        clearDistCalcRetain(distParamInfo.getDistNo());

//        initData();
        Set<String> souceTypes = new HashSet<>() ;
        for(Map.Entry<String,MbrMemberInfo> entry : this.mbrMemberInfoMap.entrySet()){

            MbrMemberInfo mbrMemberInfo = entry.getValue();
            if(mbrMemberInfo == null || mbrMemberInfo.getStatus() == null ||mbrMemberInfo.getStatus() == 3){
                continue;
            }

            String ipBaseNo = entry.getKey();

            List<MbrIpAgreement> mbrIpAgreementList = mbrIpAgreementService.getMbrIpAgreementByIpBaseNoAndRightCodeList(ipBaseNo,null,distParamInfo.getTaxYear()) ;
            if(CollectionUtils.isEmpty(mbrIpAgreementList)){
                continue;
            }

            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getDistDataCalcWorkIpRoyByDistNoAndIp(distParamInfo.getDistNo(),ipBaseNo,null);
            if(!distDataCalcWorkIpRoyList.isEmpty()) {
                souceTypes.addAll(distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getSourceType).distinct().collect(Collectors.toList()));

                Set<String> ipNameNoList = distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getIpNameNo)
                        .collect(Collectors.toSet());
                Map<String, MbrIpName> ipNameMap = mbrIpNameService
                        .getIpNameMapByIpNameNoList(new ArrayList<>(ipNameNoList));
                this.ipNameMap = ipNameMap;

                Map<String, DistDataCalcWorkIpRoy> ipBaseNoMap = new HashMap<>();
                ipBaseNoMap.put(ipBaseNo,distDataCalcWorkIpRoyList.get(0));
                this.ipBaseNoMap = ipBaseNoMap;
            }

            DistCalcRetain distCalcRetain = getDistCalcRetain(ipBaseNo, distDataCalcWorkIpRoyList,false);
            distCalcRetainService.add(distCalcRetain);
            if (distDataCalcWorkIpRoyList != null && Objects.nonNull(distCalcRetain.getId()) && distCalcRetain.getId() > 0 ) {
                distDataCalcWorkIpRoyList.forEach(x -> x.setRetainId(distCalcRetain.getId()));
                List<List<DistDataCalcWorkIpRoy>> partitions = Lists.partition(distDataCalcWorkIpRoyList, BATCH_SIZE_100);
                for(List<DistDataCalcWorkIpRoy> partition : partitions){
                    distDataCalcWorkIpRoyService.updateBatchByPrimaryKeySelective(partition);
                }
            }
        }

        for(Map.Entry<Integer,RefSociety> entry : this.allSocietyMap.entrySet()){
            String soc = entry.getKey() + "";
            RefSocietyRight societyRight = allSocietyRightMap.get(soc + "PER");
            if(societyRight == null){ //非姐妹协会不输出  TODO 没有根据权利类型取值
                continue;
            }

            List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList = distDataCalcWorkIpRoyService.getDistDataCalcWorkIpRoyByDistNoAndIp(distParamInfo.getDistNo(),null,soc);
            if(CollectionUtils.isEmpty(distDataCalcWorkIpRoyList)){
                continue;
            }

            souceTypes.addAll(distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getSourceType).distinct().collect(Collectors.toList()));

            DistCalcRetain distCalcRetain = getDistCalcRetain(soc, distDataCalcWorkIpRoyList,true);
            distCalcRetainService.add(distCalcRetain);
            if (distDataCalcWorkIpRoyList != null && Objects.nonNull(distCalcRetain.getId()) && distCalcRetain.getId() > 0 ) {
                distDataCalcWorkIpRoyList.forEach(x -> x.setRetainId(distCalcRetain.getId()));
                List<List<DistDataCalcWorkIpRoy>> partitions = Lists.partition(distDataCalcWorkIpRoyList, BATCH_SIZE_100);
                for(List<DistDataCalcWorkIpRoy> partition : partitions){
                    distDataCalcWorkIpRoyService.updateBatchByPrimaryKeySelective(partition);
                }
            }
        }

        return souceTypes.stream().collect(Collectors.toList());
    }


    private void clearDistCalcRetain(String distNo) {
        Example example = new Example(DistCalcRetain.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("distNo", distNo);
        distCalcRetainService.deleteByExample(example);
    }

    public void initData(List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyList) {
        // 初始化数据
        try{
            String year = distParamInfo.getTaxYear() ;
            if(StringUtils.isBlank(year)){
                year = "20" + distParamInfo.getDistNo().substring(1,3);
            }
            Date firstDay = new SimpleDateFormat("yyyy-MM-dd").parse(year+"-01-01");
            Date lastDay = new SimpleDateFormat("yyyy-MM-dd").parse(year+"-12-31");
            Map<String,MbrMemberInfo > mbrMemberInfoMap = mbrMemberInfoService.getAllVaildMember(firstDay,lastDay) ;
            this.mbrMemberInfoMap = mbrMemberInfoMap;
        }catch (Exception e){
            log.error("数据初始化错误：", e);
        }

        Map<String, DistDataCalcWorkIpRoy> ipBaseNoMap = distDataCalcWorkIpRoyList.stream()
                .collect(Collectors.toMap(DistDataCalcWorkIpRoy::getIpBaseNo, Function.identity(), (a, b) -> a));
        this.ipBaseNoMap = ipBaseNoMap;

        // 各国籍特殊汇率
        Map<String, BigDecimal>  refSocietyTaxRateMap = refSocietyTaxRateService.getTaxRateMap();
        this.refSocietyTaxRateMap = refSocietyTaxRateMap;

        // 各国籍特殊汇率
        Map<Integer, BigDecimal>  refMemberTaxRateMap = refMemberTaxRateService.getTaxRateMap();
        this.refMemberTaxRateMap = refMemberTaxRateMap;

        Map<Long, String> allHeadBankMap = refBankService.getAllHeadBank().stream().collect(Collectors.toMap(RefBank :: getOriginBankNo, RefBank :: getBankName));
        this.allHeadBankMap = allHeadBankMap;

        DistUpaParam distUpaParam = distUpaParamService.getByDistNo(distParamInfo.getDistNo());
        this.distUpaParam = distUpaParam;

/*        Set<String> ipNameNoList = distDataCalcWorkIpRoyList.stream().map(DistDataCalcWorkIpRoy::getIpNameNo)
                .collect(Collectors.toSet());
        Map<String, MbrIpName> ipNameMap = mbrIpNameService
                .getIpNameMapByIpNameNoList(new ArrayList<>(ipNameNoList));
        this.ipNameMap = ipNameMap;*/

        List<String> ipBaseNoList = this.mbrMemberInfoMap.keySet().stream().collect(Collectors.toList());

        // name_type = PA
        List<IpNameVO> paNames = mbrIpNameService.getPANameByIpBaseNos(ipBaseNoList);
        Map<String, IpNameVO> paNameMap = paNames.stream().collect(Collectors.toMap(IpNameVO::getIpBaseNo,Function.identity(),(a,b)-> a));
        this.paNameMap = paNameMap;

        // MUST-2627
        BigDecimal total = distParamSourceService.sumOfNetDistIncomeWithDistNo(distParamInfo.getDistNo());
        if(StringUtils.equals(distParamInfo.getUpaDist(), "1") && distUpaParam != null && total != null) {
            BigDecimal average = total.subtract(distUpaParam.getLocalTotalAmount()).divide(BigDecimal.valueOf(ipBaseNoList.size()), 0, RoundingMode.HALF_UP);
            distUpaParam.setRoyAverage(average);
        }

        List<DistRoyMbrTranDtl> distRoyMbrTranDtlList = distRoyMbrTranDtlService
                .selectDistRoyMbrTranDtlListByDist(distParamInfo.getDistNo());

        Map<String, List<DistRoyMbrTranDtl>> distRoyMbrTranDtlIpBaseNoMap = distRoyMbrTranDtlList.stream()
                .collect(Collectors.groupingBy(DistRoyMbrTranDtl::getIpBaseNo));
        this.distRoyMbrTranDtlIpBaseNoMap = distRoyMbrTranDtlIpBaseNoMap;

        List<DistRoySocTranDtl> distRoySocTranDtlList = distRoySocTranDtlService
                .selectDistRoySocTranDtlListpByDistNoList(distParamInfo.getDistNo());
        Map<String, List<DistRoySocTranDtl>> distRoySocTranDtlSocietyCodeMap = distRoySocTranDtlList.stream()
                .collect(Collectors.groupingBy(DistRoySocTranDtl::getSocietyCode));
        this.distRoySocTranDtlSocietyCodeMap = distRoySocTranDtlSocietyCodeMap;

        Map<String, MbrIp> mbrIpMap = mbrIpService.selectMapByIpBaseNo(ipBaseNoList);
        this.mbrIpMap = mbrIpMap;

        Map<String,MbrMemberMembership> mbrMemberMembershipMap = mbrMemberMembershipService.selectMbrMemberMembershipMapMapByIpBaseNoList(ipBaseNoList,distParamInfo.getTaxYear());
        this.mbrMemberMembershipMap = mbrMemberMembershipMap;

        // 会员银行信息
        Map<String, MbrMemberBankAcc> mbrMemberBankAccMap = mbrMemberBankAccService.selectMbrMemberBankAccMapByIpBaseNoList(ipBaseNoList);
        this.mbrMemberBankAccMap = mbrMemberBankAccMap;

        // 繼承人
        Map<String, MbrMemberSuccessor> mbrMemberSuccessorMap = mbrMemberSuccessorService.getAllMapByIpBaseNo(ipBaseNoList);
        this.mbrMemberSuccessorMap = mbrMemberSuccessorMap;

        // ref_pool_mstr
        Map<String, String> refPoolMstrMap = refPoolMstrService.getAllMap();
        this.refPoolMstrMap = refPoolMstrMap;
    }

    /**
         * 计算获得实体  MUST-86
     * <p>
     * //	 * @param ipNameNo
     * //	 * @param poolCode
     * //	 * @param distDataCalcWorkIpRoyList
     *
     * @return
     */
    private DistCalcRetain getDistCalcRetain(String ipBaseNo, List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyIpList,boolean isNotMust) {

//        Set<String> workUniqueKeySet = distDataCalcWorkIpRoyIpList.stream().map(DistDataCalcWorkIpRoy::getWorkUniqueKey).collect(Collectors.toSet());
//        List<DistDataCalcWorkIpRoy> list = distDataCalcWorkIpRoyService.getDistDataCalcWorkIpRoyListByDistNoAndWorkUniqueKeys(distParamInfo.getDistNo(), new ArrayList<>(workUniqueKeySet));

        DistCalcRetain distCalcRetain = setListFileTypeRoy(distDataCalcWorkIpRoyIpList);

//        setAdjRoy(distCalcRetain);

        setRoyAdjTotal(distCalcRetain);

        setMbrMember(distCalcRetain, ipBaseNo, isNotMust);

        setCommissionRate(distCalcRetain) ;

        Boolean isCheckApplyTax = false; // 用于判断apply_tax是否勾选
        setIpSoc(distCalcRetain, ipBaseNo, isCheckApplyTax, isNotMust);

        setUpaAmt(distCalcRetain);

        setOverseasDistAmount(ipBaseNo,distCalcRetain,isNotMust);

        setAmount(distCalcRetain) ;

        return distCalcRetain;
    }

    public void setCommissionRate(DistCalcRetain distCalcRetain){
        distCalcRetain.setBasicCommissionRate(distParamInfo.getCommission() == null ? BigDecimal.ZERO : distParamInfo.getCommission());
        distCalcRetain.setBasicCommissionAmt(distCalcRetain.getBasicCommissionRate().multiply(distCalcRetain.getRoyAdjTotal()).divide(new BigDecimal(-100),distParamInfo.getScale(),RoundingMode.HALF_UP));
    }

    public void setAmount(DistCalcRetain distCalcRetain){
        int scale = distParamInfo.getScale();
        // total_commission_amt(总管理费) = additional_commission_amt+ basic_commission_amt
        BigDecimal additionalCommissionAmt = distCalcRetain.getAdditionalCommissionAmt() == null ? BigDecimal.ZERO
                : distCalcRetain.getAdditionalCommissionAmt();
        BigDecimal basicCommissionAmt = distCalcRetain.getBasicCommissionAmt() == null ? BigDecimal.ZERO
                : distCalcRetain.getBasicCommissionAmt();
        distCalcRetain.setTotalCommissionAmt(additionalCommissionAmt.add(basicCommissionAmt));
        // commission_sub_total_roy（小計）=adj_sub_total_roy+transaction_taxable_income+withheld_tax+total_commission_amt
        BigDecimal rotAdjTotal = distCalcRetain.getRoyAdjTotal() == null ? BigDecimal.ZERO
                : distCalcRetain.getRoyAdjTotal();
        BigDecimal withheldTax = distCalcRetain.getWithheldTax() == null ? BigDecimal.ZERO
                : distCalcRetain.getWithheldTax();
        BigDecimal totalCommissionAmt = distCalcRetain.getTotalCommissionAmt() == null ? BigDecimal.ZERO
                : distCalcRetain.getTotalCommissionAmt();
        BigDecimal commissionSubTotalRoy = rotAdjTotal.add(withheldTax).add(totalCommissionAmt);
        distCalcRetain.setCommissionSubTotalRoy(commissionSubTotalRoy);

        BigDecimal reciprocalAmt = commissionSubTotalRoy
                .multiply(distCalcRetain.getReciprocalRate()).divide(new BigDecimal(-100),scale,RoundingMode.HALF_UP);
        distCalcRetain.setReciprocalAmt(reciprocalAmt);

        // net_payment = commission_sub_total_roy + sales_tax  + deduction  + reciprocal_amt
        BigDecimal salesTax = distCalcRetain.getSalesTax() == null ? BigDecimal.ZERO : distCalcRetain.getSalesTax();
        BigDecimal deduction = distCalcRetain.getDeduction() == null ? BigDecimal.ZERO
                : distCalcRetain.getDeduction();
        distCalcRetain.setNetPayment(commissionSubTotalRoy.add(salesTax).add(deduction).add(reciprocalAmt));
        distCalcRetain.setSalesTax(salesTax);
        if(distCalcRetain.getNetPayment().compareTo(BigDecimal.ZERO) == 0 || (distCalcRetain.getNetPayment().compareTo(new BigDecimal(500)) < 0) && distCalcRetain.getIpType().equals("N")){
            distCalcRetain.setIsPay("N");
        }else {
            distCalcRetain.setIsPay("Y");
        }

    }

    public void setOverseasDistAmount(String ipBaseNo,DistCalcRetain distCalcRetain,boolean isNotMust){
        // must协会才有的
        BigDecimal upaAmt = distCalcRetain.getUpaAmt() == null ? BigDecimal.ZERO : distCalcRetain.getUpaAmt();
        if (!isNotMust) {
            if (mbrMemberInfoMap.containsKey(ipBaseNo)) {
                MbrMemberInfo mbrMemberInfo = mbrMemberInfoMap.get(ipBaseNo);
                distCalcRetain.setDistBrNo(mbrMemberInfo.getBrNo());
                distCalcRetain.setMemberNo(mbrMemberInfo.getMemberNo());
                if (StringUtils.equals(distParamInfo.getUpaDist(), "1") && distUpaParam != null) {
                    // 循环叠加 实际分配之本地UPA金额
                    BigDecimal localDistAmount = distUpaParam.getLocalDistAmount() == null ? BigDecimal.ZERO : distUpaParam.getLocalDistAmount();
                    distUpaParam.setLocalDistAmount(upaAmt.add(localDistAmount));
                }
            }
        }else {
            if(StringUtils.equals(distParamInfo.getUpaDist(), "1") && distUpaParam != null) {
                // 循环叠加 实际分配之海外UPA金额
                BigDecimal overseasDistAmount = distUpaParam.getOverseasDistAmount() == null ? BigDecimal.ZERO : distUpaParam.getOverseasDistAmount();
                distUpaParam.setOverseasDistAmount(upaAmt.add(overseasDistAmount));
            }
        }
    }

    public void setUpaAmt(DistCalcRetain distCalcRetain){
        // upaAmt MUST-2627
        if(StringUtils.equals(distParamInfo.getUpaDist(), "1") && distUpaParam != null) {
            BigDecimal upaAmt = BigDecimal.ZERO;
            if(distCalcRetain.getNetPayment().compareTo(BigDecimal.ZERO) == 0) {
                upaAmt = distUpaParam.getLocalDefaultAmount();
            }else if(distCalcRetain.getNetPayment().compareTo(distUpaParam.getRoyAverage()) < 0) {
                upaAmt = distUpaParam.getLocalDefaultAmount().add(BigDecimal.valueOf(200));
            }else if(distCalcRetain.getNetPayment().compareTo(distUpaParam.getRoyAverage()) >= 0) {
                upaAmt = distUpaParam.getLocalDefaultAmount().add(BigDecimal.valueOf(200)).add(BigDecimal.valueOf(200))  ;
            }
            distCalcRetain.setUpaAmt(upaAmt);
        }else {
            distCalcRetain.setUpaAmt(BigDecimal.ZERO);
        }
    }

    public void setRoyAdjTotal(DistCalcRetain distCalcRetain){
        BigDecimal royAdjTotal = distCalcRetain.getSubTotalRoy()
                .add(distCalcRetain.getUpaAmt())
                .add(distCalcRetain.getAdjTvRoy())
                .add(distCalcRetain.getAdjKaraokeRoy())
                .add(distCalcRetain.getAdjAirlineRoy())
                .add(distCalcRetain.getAdjRadioRoy())
                .add(distCalcRetain.getAdjConcertRoy())
                .add(distCalcRetain.getAdjOtherPerformanceRoy())
                .add(distCalcRetain.getAdjSubTotalRoy());
        distCalcRetain.setRoyAdjTotal(royAdjTotal);
    }


    public DistCalcRetain distCalcWorkIpShareToDistCalcRetain(DistCalcRetain distCalcRetain, String ipBaseNo,Set<String> notMustSet) {
        DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = ipBaseNoMap.get(ipBaseNo);
        distCalcRetain.setDistNo(distParamInfo.getDistNo());
        distCalcRetain.setStatus(0);
        String ipType = distDataCalcWorkIpRoy.getIpType();
        /*if ("PA".equals(distDataCalcWorkIpRoy.getNameType())) {
            distCalcRetain.setPaNameNo(distDataCalcWorkIpRoy.getPaNameNo());
        }*/
        String paNameNo = distDataCalcWorkIpRoy.getPaNameNo();
        distCalcRetain.setNameNo(distDataCalcWorkIpRoy.getIpNameNo());
        distCalcRetain.setPaNameNo(paNameNo);
        /*if (paNameMap.containsKey(paNameNo)) {
            MbrIpName paName = paNameMap.get(paNameNo);
            distCalcRetain.setIpName(paName.getName());
            distCalcRetain.setIpChineseName(paName.getChineseName());
        }*/
        distCalcRetain.setIpType(ipType);
        return distCalcRetain;
    }

    /**
     * ipSoc 写入到ipBaseNo
     * TODO
     *
     * @param distCalcRetain
     * @param ipBaseNo
     */
    public void setIpSoc(DistCalcRetain distCalcRetain, String ipBaseNo, Boolean isCheckApplyTax,boolean isNotMust) {
        // 忽略 161 ref_society_right
        // reciprocal_rate: 这个是海外协会公益金比例，获取比例值从ref_society_right中的commission_rate中获取
        int scale = distParamInfo.getScale();
        boolean non_taxable_income_ind = false, commission_ind = false, withheld_tax_ind = false;
        if (isNotMust) {
            // 协会
            String ipSoc = ipBaseNo;
            distCalcRetain.setSocietyCode(Integer.valueOf(ipSoc));
            distCalcRetain.setIpType("S");
            distCalcRetain.setDistNo(distParamInfo.getDistNo());
            distCalcRetain.setStatus(0);
            Integer ipSocCode = Integer.valueOf(ipSoc);
            String socRight = ipSoc + "PER";
            if (allSocietyRightMap.containsKey(socRight)) {
                RefSocietyRight societyRight = allSocietyRightMap.get(socRight);
                distCalcRetain.setReciprocalRate(societyRight.getCommissionRate() == null ? BigDecimal.ZERO : societyRight.getCommissionRate());
            }

            if (allSocietyMap.containsKey(ipSocCode)) {
                RefSociety refSociety = allSocietyMap.get(ipSocCode);
                distCalcRetain.setPayableCurrencyCode(refSociety.getSocietyCurrencyCode());
                distCalcRetain.setAccountName(refSociety.getBankAccountName());
                distCalcRetain.setSocietyName(refSociety.getSocietyName());
                distCalcRetain.setIpChineseName(refSociety.getSocietyName());

                BigDecimal taxRate = refSocietyTaxRateMap.get(refSociety.getCountryCode());
                if(taxRate != null){
                    distCalcRetain.setTaxRate(taxRate);
                }else {
                    distCalcRetain.setTaxRate(distParamInfo.getOverseas());
                }
            }

            String socKey = StringUtils.leftPad(ipSoc + "", 3, "0");
            if (distRoySocTranDtlSocietyCodeMap.containsKey(socKey)) {
                List<DistRoySocTranDtl> distRoySocTranDtlIpBaseNoList = distRoySocTranDtlSocietyCodeMap.get(socKey);
                // 4 = deduction是否重新計算稅率直接更改
                // commission_ind = Apply Comm
                // withheld_tax_ind = Apply Tax
                // add_after_dist_ind = After Dist
                Map<Object, DistRoySocTranDtl> tranTypeMap = distRoySocTranDtlIpBaseNoList.stream()
                        .collect(Collectors.toMap(tran -> {
                            if ("Y".equals(tran.getNonTaxableIncomeInd())) {
                                return 0;
                            } else if (StringUtils.equalsAnyIgnoreCase("Y", tran.getCommissionInd(), tran.getWithheldTaxInd())) {
                                return -1;
                            }
                            return tran.getTranType();
                        }, Function.identity(),(a,b)-> a));
                if (tranTypeMap.containsKey(0)) {
                    // non_taxable_income_ind =Y 写入 deduction
                    non_taxable_income_ind = true;
                    distCalcRetain.setDeduction(tranTypeMap.get(0).getTranAmt());
                    // transaction_taxable_income：withheld_tax_ind有勾选或者commission_ind勾选，
//                    distCalcRetain.setTransactionTaxableIncome(tranTypeMap.get(0).getTranAmt());
                }else if (tranTypeMap.containsKey(-1)) {
                    // transaction_taxable_income：withheld_tax_ind有勾选或者commission_ind勾选，
                    DistRoySocTranDtl distRoySocTranDtl = tranTypeMap.get(-1);
                    BigDecimal transactionTaxableIncome = distRoySocTranDtl.getTranAmt();
                    distCalcRetain.setTransactionTaxableIncome(transactionTaxableIncome);
                    if (StringUtils.equalsIgnoreCase("Y", distRoySocTranDtl.getCommissionInd())) {
                        //  如果 commission_ind = Y
                        // Total Commission Amt =(Royalties Adjustment Total + Transaction Taxable Income) * Basic Commission Rate
                        //Withheld Tax = (Royalties Adjustment Total + Transaction Taxable Income) * Tax Rate
                        commission_ind = true;
                    } else {
                        // withheldTaxInd = Y
                        //Sales Tax	= (Royalties Adjustment Total + Transaction Taxable Income) * Sales Tax Rate
                        withheld_tax_ind = true;
                    }
                }
            }
        } else {
            // 个人
            DistDataCalcWorkIpRoy distDataCalcWorkIpRoy = ipBaseNoMap.get(ipBaseNo);
            if(distDataCalcWorkIpRoy != null){
                distCalcRetain.setNameNo(distDataCalcWorkIpRoy.getIpNameNo());
                distCalcRetain.setSocietyCode(Integer.valueOf(distDataCalcWorkIpRoy.getIpSocietyCode()));
            }
            String ipType = mbrIpMap.get(ipBaseNo).getIpType();
            IpNameVO paNameNo = paNameMap.get(ipBaseNo);
            if(paNameNo != null){
                distCalcRetain.setPaNameNo(paNameNo.getNameNo());
                distCalcRetain.setIpName(paNameNo.getName());
                String chineseName = paNameNo.getChineseName();
                if(StringUtils.isEmpty(chineseName)){
                    chineseName = mbrIpNameService.getChineseByIpbaseNo(ipBaseNo);
                }
                distCalcRetain.setIpChineseName(chineseName);
            }
            distCalcRetain.setIpType(ipType);
            distCalcRetain.setStatus(0);
            if(ipType.equals("L")){
                distCalcRetain.setSalesTaxRate(distParamInfo.getSalesTaxRate());
            }
//			dist_roy_mbr_tran_dtl 根据  tran_type 写入对应的字段
            if (distRoyMbrTranDtlIpBaseNoMap.containsKey(ipBaseNo)) {
                List<DistRoyMbrTranDtl> distRoyMbrTranDtlIpBaseNoList = distRoyMbrTranDtlIpBaseNoMap.get(ipBaseNo);
                Map<Object, DistRoyMbrTranDtl> tranTypeMap = distRoyMbrTranDtlIpBaseNoList.stream()
                        .collect(Collectors.toMap(tran -> {
                            if ("Y".equals(tran.getNonTaxableIncomeInd())) {
                                return 0;
                            }
                            if (StringUtils.equalsAnyIgnoreCase("Y", tran.getCommissionInd(), tran.getWithheldTaxInd())) {
                                return -1;
                            }
                            return tran.getTranType();
                        }, Function.identity(),(a,b)-> a));
                if (tranTypeMap.containsKey(0)) {
                    // non_taxable_income_ind =Y 写入 deduction
                    non_taxable_income_ind = true;
                    distCalcRetain.setDeduction(tranTypeMap.get(0).getTranAmt());
                }
                if (tranTypeMap.containsKey(-1)) {
                    // transaction_taxable_income：withheld_tax_ind有勾选或者commission_ind勾选，
                    DistRoyMbrTranDtl distRoyMbrTranDtl = tranTypeMap.get(-1);
                    BigDecimal transactionTaxableIncome = distRoyMbrTranDtl.getTranAmt();
                    distCalcRetain.setTransactionTaxableIncome(transactionTaxableIncome);
                    if (StringUtils.equalsIgnoreCase("Y", distRoyMbrTranDtl.getCommissionInd())) {
                        //  如果 commission_ind = Y
                        // Total Commission Amt =(Royalties Adjustment Total + Transaction Taxable Income) * Basic Commission Rate
                        //Withheld Tax = (Royalties Adjustment Total + Transaction Taxable Income) * Tax Rate
                        commission_ind = true;
                    } else {
                        // withheldTaxInd = Y
                        //Sales Tax	= (Royalties Adjustment Total + Transaction Taxable Income) * Sales Tax Rate
                        withheld_tax_ind = true;
                    }
                }
            }
            if (mbrMemberBankAccMap.containsKey(ipBaseNo)) {
                // TODO 有问题，继承人的 没考虑进去，同时mbrMenmerBankAccMap 取的总是第一个，会有多个的情况，要考虑继承人，继承人的有效时间包含清单时间（交差集问题？？？），暂时未明确
                MbrMemberBankAcc mbrMemberBankAcc = mbrMemberBankAccMap.get(ipBaseNo);
                distCalcRetain.setPayableCurrencyCode(mbrMemberBankAcc.getCurrencyCode());

                String payablePaymentMethod = mbrMemberBankAcc.getPaymentMethod();
                distCalcRetain.setPayablePaymentMethod(payablePaymentMethod);
                String payableType = "";
                if(StringUtils.isNotEmpty(payablePaymentMethod)){
                    if(payablePaymentMethod.equalsIgnoreCase("A")){
                        payableType = mbrMemberBankAcc.getBankName();
                        if(StringUtils.isEmpty(payableType) && mbrMemberBankAcc.getBankNo() != null ){
                            payableType = allHeadBankMap.get(Long.valueOf(mbrMemberBankAcc.getBankNo()));
                        }
                    }else if(payablePaymentMethod.equalsIgnoreCase("C")){
                        payableType = "支票";
                    }else {
                        payableType = "其他";
                    }
                }
                distCalcRetain.setPayableType(payableType);
                distCalcRetain.setAccountName(mbrMemberBankAcc.getAccountName());
                distCalcRetain.setPaymentDesc(mbrMemberBankAcc.getPaymentDesc());

               String taxType = mbrMemberBankAcc.getIpType();
               String nationality = mbrIpNationalityService.getNationalityByIpbaseNo(ipBaseNo);
                BigDecimal subTotalRoy = distCalcRetain.getSubTotalRoy();
               if(StringUtils.equalsIgnoreCase(ipType,"L")){
                   distCalcRetain.setTaxRate(BigDecimal.ZERO);
               }else if(nationality.equals("TW")){
                    if(StringUtils.isEmpty(taxType)){
                        if (subTotalRoy.compareTo(distParamInfo.getLocalLimit()) == 1 ) {
                            distCalcRetain.setTaxRate(BigDecimal.TEN);
                        }
                        distCalcRetain.setTaxType(1);
                    }else if (StringUtils.equalsIgnoreCase(taxType,"NO")) {
                        MbrMemberInfo mbrMemberInfo = mbrMemberInfoMap.get(ipBaseNo);
                        BigDecimal taxRate = distParamInfo.getOverseas();
                        if(StringUtils.equalsIgnoreCase(mbrMemberInfo.getSpecialExchangeRate(),"Y")){
                            distCalcRetain.setTaxRate(taxRate);
                            BigDecimal discountTaxRate =  refSocietyTaxRateMap.get(nationality );
                            /*if(discountTaxRate == null){
                                discountTaxRate = BigDecimal.ZERO;
                            }
                            if(!non_taxable_income_ind){
                                BigDecimal deduction = subTotalRoy.multiply(taxRate.subtract(discountTaxRate)).divide(new BigDecimal(100),scale,RoundingMode.HALF_UP);
                                distCalcRetain.setDeduction(deduction);
                            }*/
                            if(discountTaxRate != null){
                                distCalcRetain.setTaxRate(discountTaxRate);
                            }
                            distCalcRetain.setTaxType(3);
                        }else {
                            distCalcRetain.setTaxRate(taxRate);
                            distCalcRetain.setTaxType(2);
                        }
                    }
               }else {
                   MbrMemberInfo mbrMemberInfo = mbrMemberInfoMap.get(ipBaseNo);
                   BigDecimal taxRate = distParamInfo.getOverseas();
                   if(StringUtils.isEmpty(taxType)){
                       if(StringUtils.equalsIgnoreCase(mbrMemberInfo.getSpecialExchangeRate(),"Y")){
                           distCalcRetain.setTaxRate(taxRate);
                           BigDecimal discountTaxRate =  refSocietyTaxRateMap.get(nationality );
                           /*if(discountTaxRate == null){
                               discountTaxRate = BigDecimal.ZERO;
                           }
                           if(!non_taxable_income_ind){
                               BigDecimal deduction = subTotalRoy.multiply(taxRate.subtract(discountTaxRate)).divide(new BigDecimal(100),scale,RoundingMode.HALF_UP);
                               distCalcRetain.setDeduction(deduction);
                           }*/
                           if(discountTaxRate != null){
                               distCalcRetain.setTaxRate(discountTaxRate);
                           }
                           distCalcRetain.setTaxType(5);
                       }else {
                           distCalcRetain.setTaxRate(taxRate);
                           distCalcRetain.setTaxType(4);
                       }

                   } else if(StringUtils.equalsIgnoreCase(taxType,"NL")){
                       if (subTotalRoy.compareTo(distParamInfo.getLocalLimit()) == 1 ) {
                           distCalcRetain.setTaxRate(BigDecimal.TEN);
                       }
                       distCalcRetain.setTaxType(6);
                   }
               }
            }
        }

        if(commission_ind){
            BigDecimal transactionTaxableIncome = distCalcRetain.getTransactionTaxableIncome();
            distCalcRetain.setBasicCommissionAmt(distCalcRetain.getRoyAdjTotal().add(transactionTaxableIncome)
                    .multiply(distCalcRetain.getBasicCommissionRate()).divide(new BigDecimal(-100),scale,RoundingMode.HALF_UP));
            distCalcRetain.setWithheldTax(distCalcRetain.getRoyAdjTotal().add(transactionTaxableIncome).multiply(distCalcRetain.getTaxRate()).divide(new BigDecimal(-100),scale,RoundingMode.HALF_UP));
        } else {
            distCalcRetain.setBasicCommissionAmt(distCalcRetain.getRoyAdjTotal().multiply(distCalcRetain.getBasicCommissionRate()).divide(new BigDecimal(-100),scale,RoundingMode.HALF_UP));
            distCalcRetain.setWithheldTax(distCalcRetain.getRoyAdjTotal().multiply(distCalcRetain.getTaxRate()).divide(new BigDecimal(-100),scale,RoundingMode.HALF_UP));
        }

        if(withheld_tax_ind){
            distCalcRetain.setSalesTax(distCalcRetain.getRoyAdjTotal().add(distCalcRetain.getTransactionTaxableIncome())
                    .multiply(distCalcRetain.getSalesTaxRate()).divide(new BigDecimal(100),scale,RoundingMode.HALF_UP));
        } else if(distCalcRetain.getSalesTaxRate().compareTo(BigDecimal.ZERO) == 1){
            distCalcRetain.setSalesTax(distCalcRetain.getRoyAdjTotal().multiply(distCalcRetain.getSalesTaxRate()).divide(new BigDecimal(100),scale,RoundingMode.HALF_UP));
        }
        distCalcRetain.setIpBaseNo(ipBaseNo);
    }

    public void setTaxRate(DistCalcRetain distCalcRetain, String ipBaseNo, Set<String> noMustSet) {

        // tax_rate的计算：根据dist_param_info.local_limit
        // 阈值，如果sub_totoal_roy金额小于localLimit, 取below_limit_rate如果金额大于这个值取above_limit_rate.
        BigDecimal localLimit = distParamInfo.getLocalLimit();
        BigDecimal subTotalRoy = distCalcRetain.getSubTotalRoy();
        BigDecimal belowLimitRate = distParamInfo.getBelowLimitRate();
        BigDecimal aboveLimitRate = distParamInfo.getAboveLimitRate();
        if(noMustSet.contains(ipBaseNo)){ //
            BigDecimal taxRate = refSocietyTaxRateMap.get(ipBaseNo);
            if(taxRate != null){
                distCalcRetain.setTaxRate(taxRate);
            }else {
                distCalcRetain.setTaxRate(distParamInfo.getSocietyTaxRate());
            }
        }else {
            // TODO zhy
            if (subTotalRoy.compareTo(localLimit) < 0) {
                distCalcRetain.setTaxRate(belowLimitRate);
            } else {
                distCalcRetain.setTaxRate(distParamInfo.getAboveLimitRate());
            }

            BigDecimal withheldTax = distCalcRetain.getRoyAdjTotal().multiply(distCalcRetain.getTaxRate()).divide(new BigDecimal(100))
                    .multiply(new BigDecimal(-1));
            distCalcRetain.setWithheldTax(withheldTax);
        }
        //	basic_commission_rate = dist_param_info.commission
        distCalcRetain.setBasicCommissionRate(distParamInfo.getCommission() == null ? BigDecimal.ZERO : distParamInfo.getCommission());
        distCalcRetain.setBasicCommissionAmt(distCalcRetain.getBasicCommissionRate().multiply(distCalcRetain.getRoyAdjTotal()).divide(new BigDecimal(-100)));
    }

    public void setMbrMember(DistCalcRetain distCalcRetain, String ipBaseNo, boolean isNotMust) {

        // additional_commission_rate追加管理费（如果ip是must的准会员，会扣取这部分比例费用，对应dist_param_info.extra  ）
        // 判断准会员还是正式会员，通过mbr_member_membership表当前有效期内type值（AW、AP表示是准会员）
        if (!isNotMust) {
            MbrMemberInfo mbrMemberInfo = mbrMemberInfoMap.get(ipBaseNo);
            if(null == mbrMemberInfo ){
                return;
            }

            if(mbrMemberSuccessorMap.containsKey(ipBaseNo)){
                distCalcRetain.setSuccessorName(mbrMemberSuccessorMap.get(ipBaseNo).getName());
            }

            MbrMemberMembership mbrMemberMembership = mbrMemberMembershipMap.get(ipBaseNo);
            String type = mbrMemberInfo.getMembership();
            int scale = distParamInfo.getScale();
            if(mbrMemberMembership != null){
                type = mbrMemberMembership.getType();
            }
            if ("AW".equals(type) || "AP".equals(type)) {
                BigDecimal additionalCommissionRate = distParamInfo.getExtra();
                distCalcRetain.setAdditionalCommissionRate(additionalCommissionRate);
                // additional_commission_amt= adj_sub_total_roy * additional_commission_rate 计算
                BigDecimal additionalCommissionAmt = distCalcRetain.getRoyAdjTotal().multiply(additionalCommissionRate).divide(new BigDecimal(100),scale,RoundingMode.HALF_UP)
                        .multiply(new BigDecimal(-1));
                distCalcRetain.setAdditionalCommissionAmt(additionalCommissionAmt);
            }
        }

    }

    /**
     * 设置金额
     */
    public void setAdjRoy(DistCalcRetain distCalcRetain) {
        // adj_*相关的金额，调整分配时才会设值，默认为 0
        distCalcRetain.setAdjTvRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjConcertRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjKaraokeRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjRadioRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjAirlineRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjOtherPerformanceRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjSubTotalRoy(BigDecimal.ZERO);
        // adj_sub_total_roy = adj_tv_roy + adj_concert_roy + adj_karaoke_roy + adj_radio_roy + adj_airline_roy + adj_other_performance_roy + adj_sub_total_roy
    }

    /**
     * 设置金额
     *
     * @param distDataCalcWorkIpRoyIpList
     * @return
     */
    public DistCalcRetain setListFileTypeRoy(List<DistDataCalcWorkIpRoy> distDataCalcWorkIpRoyIpList) {
        DistCalcRetain distCalcRetain = new DistCalcRetain();
        distCalcRetain.init();
        distCalcRetain.setDistNo(distParamInfo.getDistNo());
//        distCalcRetain.setIsPay("N");
        this.initAmount(distCalcRetain);
        if(distDataCalcWorkIpRoyIpList == null){
            return distCalcRetain;
        }
        Map<String, List<DistDataCalcWorkIpRoy>> ipNameNoPoolCodeGroup = distDataCalcWorkIpRoyIpList.stream()
                .collect(Collectors.groupingBy(DistDataCalcWorkIpRoy::getPoolCode));
        BigDecimal others = BigDecimal.ZERO;
        BigDecimal subTotalRoy = BigDecimal.ZERO;
        for (String poolCode : ipNameNoPoolCodeGroup.keySet()) {
            List<DistDataCalcWorkIpRoy> ipNameNoPoolCodeList = ipNameNoPoolCodeGroup.get(poolCode);
            BigDecimal amountSum = ipNameNoPoolCodeList.stream().filter(it -> it.getNetPoint() != null)
                    .map(DistDataCalcWorkIpRoy::getDistRoy).reduce(BigDecimal.ZERO,BigDecimal::add);


            String desc = refPoolMstrMap.getOrDefault(poolCode, null);
            if (StringUtils.isNotBlank(desc)) {
                Map<String, BigDecimal> map = new HashMap<>();
                String royExtJson = distCalcRetain.getRoyExtJson();
                if (StringUtils.isNotBlank(distCalcRetain.getRoyExtJson())) {
                    map = JSON.parseObject(royExtJson, HashMap.class);
                }
                map.put(desc, amountSum);
                distCalcRetain.setRoyExtJson(JSON.toJSONString(map));
                setRoyByPoolDesc(distCalcRetain,desc,amountSum);
            } else {
                // poolCode不存在ref_pool_mstr的情况不应该出现，这里加个保险，以防万一
                log.error(String.format("bug:::该情况不应该出现，poolCode【%s】不存在于ref_pool_mstr表中", poolCode));
                others = others.add(amountSum);
            }
            subTotalRoy = subTotalRoy.add(amountSum);
        }
        if (others.compareTo(BigDecimal.ZERO) > 0) {
            subTotalRoy = subTotalRoy.add(others);
        }
        // sub_total_roy =  tv_roy+radio_roy+concert_roy+karaoke_film_roy+airline_roy+other_performance_roy
        distCalcRetain.setSubTotalRoy(subTotalRoy);
        return distCalcRetain;
    }

    public void initAmount(DistCalcRetain distCalcRetain){
        distCalcRetain.setTvRoy(BigDecimal.ZERO);
        distCalcRetain.setConcertRoy(BigDecimal.ZERO);
        distCalcRetain.setRadioRoy(BigDecimal.ZERO);
        distCalcRetain.setAirlineRoy(BigDecimal.ZERO);
        distCalcRetain.setKaraokeFilmRoy(BigDecimal.ZERO);
        distCalcRetain.setOtherPerformanceRoy(BigDecimal.ZERO);
        distCalcRetain.setSubTotalRoy(BigDecimal.ZERO);

        distCalcRetain.setSalesTaxRate(BigDecimal.ZERO);
        distCalcRetain.setSalesTax(BigDecimal.ZERO);
        distCalcRetain.setTaxRate(BigDecimal.ZERO);
        distCalcRetain.setRevisedWithheldTax(BigDecimal.ZERO);

        distCalcRetain.setBasicCommissionRate(BigDecimal.ZERO);
        distCalcRetain.setBasicCommissionAmt(BigDecimal.ZERO);
        distCalcRetain.setAdditionalCommissionRate(BigDecimal.ZERO);
        distCalcRetain.setAdditionalCommissionAmt(BigDecimal.ZERO);
        distCalcRetain.setRevisedCommissionAmt(BigDecimal.ZERO);

        distCalcRetain.setReciprocalRate(BigDecimal.ZERO);
        distCalcRetain.setReciprocalAmt(BigDecimal.ZERO);
        distCalcRetain.setDeduction(BigDecimal.ZERO);
        distCalcRetain.setTransactionTaxableIncome(BigDecimal.ZERO);


        distCalcRetain.setAdjTvRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjConcertRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjKaraokeRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjRadioRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjAirlineRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjOtherPerformanceRoy(BigDecimal.ZERO);
        distCalcRetain.setAdjSubTotalRoy(BigDecimal.ZERO);

        distCalcRetain.setUpaAmt(BigDecimal.ZERO);

    }

    public void setRoyByPoolDesc(DistCalcRetain distCalcRetain, String poolDesc, BigDecimal amount){
        switch (poolDesc){
            case "RADIO":
                distCalcRetain.setRadioRoy(amount);
                break;
            case "TV":
                distCalcRetain.setTvRoy(amount);
                break;
            case "CONCERT":
                distCalcRetain.setConcertRoy(amount);
                break;
            case "AIRLINE":
                distCalcRetain.setAirlineRoy(amount);
                break;
            case "KARAOKE":
            case "Film":
                amount = amount.add(distCalcRetain.getKaraokeFilmRoy());
                distCalcRetain.setKaraokeFilmRoy(amount);
                break;
            case "Download":
            case "OTHERS":
            case "PUBLIC TRANSMISSION":
                amount = amount.add(distCalcRetain.getOtherPerformanceRoy());
                distCalcRetain.setOtherPerformanceRoy(amount);
                break;
        }

    }

}
