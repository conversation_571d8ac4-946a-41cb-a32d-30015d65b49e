package tw.org.must.must.model.distdata;

import tw.org.must.must.common.base.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "`dist_data_calc_work_ip_roy`")
public class DistDataCalcWorkIpRoy extends BaseEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = 8333326625040816205L;

	@Column(name = "`point_id`")
	private Long pointId;

	public Long getPointId() {
		return pointId;
	}

	public void setPointId(Long pointId) {
		this.pointId = pointId;
	}

	/**
	 * 分配编号
	 */
	@Column(name = "`dist_no`")
	private String distNo;

	/**
	 * list_category
	 */
	@Column(name = "`category_code`")
	private String categoryCode;

	/**
	 * category对应的pool
	 */
	@Column(name = "`pool_code`")
	private String poolCode;

	/**
	 * 
	 */
	@Column(name = "`pool_right`")
	private String poolRight;

	/**
	 * 作品id
	 */
	@Column(name = "`work_id`")
	private Long workId;

	/**
	 * 作品协会
	 */
	@Column(name = "`work_society_code`")
	private Integer workSocietyCode;

	/**
	 * 
	 */
	@Column(name = "`work_unique_key`")
	private String workUniqueKey;

	/**
	 * 匹配的标题id
	 */
	@Column(name = "`title_id`")
	private Long titleId;
	
	@Column(name = "`ip_base_no`")
	private String ipBaseNo;

	/**
	 * work_ip_share.ip_name_no
	 */
	
	
	@Column(name = "`ip_name_no`")
	private String ipNameNo;

	/**
	 * 
	 */
	@Column(name = "`pa_name_no`")
	private String paNameNo;

	/**
	 * ip name type
	 */
	@Column(name = "`name_type`")
	private String nameType;
	
	@Column(name = "`ip_type`")
	private String ipType;
	
	
	

	/**
	 * work ip role：C、CA、A ....etc
	 */
	@Column(name = "`work_ip_role`")
	private String workIpRole;

	/**
	 * ip share ip soc
	 */
	@Column(name = "`ip_society_code`")
	private String ipSocietyCode;

	/**
	 * work_ip_share.ip_share
	 */
	@Column(name = "`ip_share`")
	private BigDecimal ipShare;

	/**
	 * 使用类别，
	 */
	@Column(name = "`usage`")
	private String usage;

	/**
	 * 作品类别：ADP、ORG ...etc
	 */
	@Column(name = "`work_type`")
	private String workType;

	/**
	 * ADP 、ARR作品的ot work id
	 */
	@Column(name = "`ref_work_id`")
	private Long refWorkId;

	/**
	 * ADP 、ARR作品的ot work soc
	 */
	@Column(name = "`ref_work_society`")
	private Integer refWorkSociety;

	/**
	 * 如果是AV类作品分配，指向父 av作品id
	 */
	@Column(name = "`av_work_id`")
	private Long avWorkId;

	/**
	 * 如果是AV类作品分配，指向父 av作品soc
	 */
	@Column(name = "`av_work_society`")
	private Integer avWorkSociety;

	/**
	 * 
	 */
	@Column(name = "`av_title_id`")
	private Long avTitleId;

	/**
	 * 总积点数
	 */
	@Column(name = "`gross_point`")
	private BigDecimal grossPoint;

	/**
	 * 可分配积点数
	 */
	@Column(name = "`net_point`")
	private BigDecimal netPoint;

	/**
	 * 分配金额，根据积点计算
	 */
	@Column(name = "`dist_roy`")
	private BigDecimal distRoy;

	/**
	 * 表演日期
	 */
	@Column(name = "`perform_date`")
	private Date performDate;

	/**
	 * work_ip_share.group_indicator
	 */
	@Column(name = "`group_indicator`")
	private String groupIndicator;

	/**
	 * work_ip_share.sd
	 */
	@Column(name = "`sd`")
	private String sd;

	/**
	 * 是否可分配，Y or N
	 */
	@Column(name = "`is_dist`")
	private String isDist;

	/**
	 * 数据来源表：P、一般清单，O、海外清单，DR、sdsr清单
	 */
	@Column(name = "`source_type`")
	private String sourceType;

	@Column(name = "`file_mapping_id`")
	private Long fileMappingId;

	@Column(name = "`file_base_id`")
	private Long fileBaseId;

	@Column(name = "`right_type`")
	private String rightType;

	@Column(name = "`retain_id`")
	private Long retainId;

//	@Column(name="`ext_json`")
	private String extJson;

//	@Column(name="`source_name`")
	private String sourceName;

	@Transient
	private BigDecimal upaAmt ;

	@Transient
	private Long pointMappingId;

	public String getSourceType() {
		return sourceType;
	}

	public void setSourceType(String sourceType) {
		this.sourceType = sourceType;
	}

	public Long getRetainId() {
		return retainId;
	}

	public void setRetainId(Long retainId) {
		this.retainId = retainId;
	}


	public String getDistNo() {
		return distNo;
	}

	public void setDistNo(String distNo) {
		this.distNo = distNo;
	}

	public String getCategoryCode() {
		return categoryCode;
	}

	public void setCategoryCode(String categoryCode) {
		this.categoryCode = categoryCode;
	}

	public String getPoolCode() {
		return poolCode;
	}

	public void setPoolCode(String poolCode) {
		this.poolCode = poolCode;
	}

	public String getPoolRight() {
		return poolRight;
	}

	public void setPoolRight(String poolRight) {
		this.poolRight = poolRight;
	}

	public Long getWorkId() {
		return workId;
	}

	public void setWorkId(Long workId) {
		this.workId = workId;
	}

	public Integer getWorkSocietyCode() {
		return workSocietyCode;
	}

	public void setWorkSocietyCode(Integer workSocietyCode) {
		this.workSocietyCode = workSocietyCode;
	}

	public String getWorkUniqueKey() {
		return workUniqueKey;
	}

	public void setWorkUniqueKey(String workUniqueKey) {
		this.workUniqueKey = workUniqueKey;
	}

	public Long getTitleId() {
		return titleId;
	}

	public void setTitleId(Long titleId) {
		this.titleId = titleId;
	}

	public String getIpNameNo() {
		return ipNameNo;
	}

	public void setIpNameNo(String ipNameNo) {
		this.ipNameNo = ipNameNo;
	}

	public String getPaNameNo() {
		return paNameNo;
	}

	public void setPaNameNo(String paNameNo) {
		this.paNameNo = paNameNo;
	}

	public String getNameType() {
		return nameType;
	}

	public void setNameType(String nameType) {
		this.nameType = nameType;
	}

	public String getWorkIpRole() {
		return workIpRole;
	}

	public void setWorkIpRole(String workIpRole) {
		this.workIpRole = workIpRole;
	}

	public String getIpSocietyCode() {
		return ipSocietyCode;
	}

	public void setIpSocietyCode(String ipSocietyCode) {
		this.ipSocietyCode = ipSocietyCode;
	}

	public BigDecimal getIpShare() {
		return ipShare;
	}

	public void setIpShare(BigDecimal ipShare) {
		this.ipShare = ipShare;
	}

	public String getUsage() {
		return usage;
	}

	public void setUsage(String usage) {
		this.usage = usage;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	public Long getRefWorkId() {
		return refWorkId;
	}

	public void setRefWorkId(Long refWorkId) {
		this.refWorkId = refWorkId;
	}

	public Integer getRefWorkSociety() {
		return refWorkSociety;
	}

	public void setRefWorkSociety(Integer refWorkSociety) {
		this.refWorkSociety = refWorkSociety;
	}

	public Long getAvWorkId() {
		return avWorkId;
	}

	public void setAvWorkId(Long avWorkId) {
		this.avWorkId = avWorkId;
	}

	public Integer getAvWorkSociety() {
		return avWorkSociety;
	}

	public void setAvWorkSociety(Integer avWorkSociety) {
		this.avWorkSociety = avWorkSociety;
	}

	public Long getAvTitleId() {
		return avTitleId;
	}

	public void setAvTitleId(Long avTitleId) {
		this.avTitleId = avTitleId;
	}

	public BigDecimal getGrossPoint() {
		return grossPoint;
	}

	public void setGrossPoint(BigDecimal grossPoint) {
		this.grossPoint = grossPoint;
	}

	public BigDecimal getNetPoint() {
		return netPoint;
	}

	public void setNetPoint(BigDecimal netPoint) {
		this.netPoint = netPoint;
	}

	public BigDecimal getDistRoy() {
		return distRoy;
	}

	public void setDistRoy(BigDecimal distRoy) {
		this.distRoy = distRoy;
	}

	public Date getPerformDate() {
		return performDate;
	}

	public void setPerformDate(Date performDate) {
		this.performDate = performDate;
	}

	public String getGroupIndicator() {
		return groupIndicator;
	}

	public void setGroupIndicator(String groupIndicator) {
		this.groupIndicator = groupIndicator;
	}

	public String getSd() {
		return sd;
	}

	public void setSd(String sd) {
		this.sd = sd;
	}

	public String getIsDist() {
		return isDist;
	}

	public void setIsDist(String isDist) {
		this.isDist = isDist;
	}

	public Long getFileMappingId() {
		return fileMappingId;
	}

	public void setFileMappingId(Long fileMappingId) {
		this.fileMappingId = fileMappingId;
	}

	public Long getFileBaseId() {
		return fileBaseId;
	}

	public void setFileBaseId(Long fileBaseId) {
		this.fileBaseId = fileBaseId;
	}

	public String getIpBaseNo() {
		return ipBaseNo;
	}

	public void setIpBaseNo(String ipBaseNo) {
		this.ipBaseNo = ipBaseNo;
	}

	public String getIpType() {
		return ipType;
	}

	public void setIpType(String ipType) {
		this.ipType = ipType;
	}

	public String getRightType() {
		return rightType;
	}

	public void setRightType(String rightType) {
		this.rightType = rightType;
	}

	public BigDecimal getUpaAmt() {
		return upaAmt;
	}

	public void setUpaAmt(BigDecimal upaAmt) {
		this.upaAmt = upaAmt;
	}

	public String getExtJson() {
		return extJson;
	}

	public void setExtJson(String extJson) {
		this.extJson = extJson;
	}

	public String getSourceName() {
		return sourceName;
	}

	public void setSourceName(String sourceName) {
		this.sourceName = sourceName;
	}

	public Long getPointMappingId() {
		return pointMappingId;
	}

	public void setPointMappingId(Long pointMappingId) {
		this.pointMappingId = pointMappingId;
	}
}